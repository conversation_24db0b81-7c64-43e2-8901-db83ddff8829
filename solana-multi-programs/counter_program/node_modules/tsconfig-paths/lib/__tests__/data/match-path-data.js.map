{"version": 3, "file": "match-path-data.js", "sourceRoot": "", "sources": ["../../../src/__tests__/data/match-path-data.ts"], "names": [], "mappings": ";;;AAAA,6BAAqC;AACrC,+CAAmD;AAiBnD,IAAM,oCAAoC,GAAG;IAC3C,KAAK;IACL,OAAO;IACP,OAAO;IACP,KAAK;IACL,MAAM;CACP,CAAC;AAEW,QAAA,KAAK,GAA2B;IAC3C;QACE,IAAI,EAAE,sDAAsD;QAC5D,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE;YACL,OAAO,EAAE,CAAC,YAAY,CAAC;SACxB;QACD,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/D,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrE,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,wDAAwD;QAC9D,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE;YACL,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;SACtD;QACD,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/D,eAAe,EAAE,WAAW;QAC5B,UAAU,EAAE,CAAC,KAAK,CAAC;QACnB,YAAY,EAAE,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;KACtE;IACD;QACE,IAAI,EACF,sFAAsF;QACxF,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE;YACL,GAAG,EAAE,CAAC,YAAY,CAAC;YACnB,OAAO,EAAE,CAAC,YAAY,CAAC;SACxB;QACD,aAAa,EAAE;YACb,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,CAAC;YACrD,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;SAC/C;QACD,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrE,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,qEAAqE;QAC3E,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QACzD,eAAe,EAAE,WAAW;QAC5B,UAAU,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;QAC7B,YAAY,EAAE,IAAA,4BAAe,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;KACxE;IACD;QACE,IAAI,EAAE,iDAAiD;QACvD,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACtD,eAAe,EAAE,cAAc;QAC/B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;QACnD,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,yDAAyD;QAC/D,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE;YACL,SAAS,EAAE,CAAC,cAAc,CAAC;SAC5B;QACD,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACpD,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,IAAA,4BAAe,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClE,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,+DAA+D;QACrE,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/D,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACrE,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,gDAAgD;QACtD,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/D,WAAW,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;QACnC,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;QAC5D,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,qDAAqD;QAC3D,eAAe,EAAE,OAAO;QACxB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAClE,WAAW,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;QACnC,eAAe,EAAE,cAAc;QAC/B,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;KAChE;IACD;QACE,IAAI,EAAE,gEAAgE;QACtE,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;QAC1C,WAAW,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE;QAC3D,aAAa,EAAE;YACb,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;YAC/C,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE,kBAAkB;SACvE;QACD,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1B,eAAe,EAAE,aAAa;QAC9B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC;KACjE;IACD;QACE,IAAI,EAAE,+DAA+D;QACrE,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,UAAU,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;QAC/B,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACjE,eAAe,EAAE,aAAa;QAC9B,WAAW,EAAE;YACX,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,WAAW;SACrB;QACD,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;KAC/D;IACD;QACE,IAAI,EAAE,uDAAuD;QAC7D,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE;YACb,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;YAChD,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC;SACnD;QACD,eAAe,EAAE,aAAa;QAC9B,WAAW,EAAE;YACX,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE;SACnE;QACD,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1B,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;KAC/D;IACD;QACE,IAAI,EAAE,oEAAoE;QAC1E,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE;QACT,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACnD,eAAe,EAAE,OAAO;QACxB,YAAY,EAAE,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACzD,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,+DAA+D;QACrE,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE;QACT,WAAW,EAAE,KAAK;QAClB,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACnD,eAAe,EAAE,OAAO;QACxB,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,4CAA4C;QAClD,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QAClC,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAClD,eAAe,EAAE,OAAO;QACxB,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,8CAA8C;QACpD,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE;YACL,OAAO,EAAE,CAAC,YAAY,CAAC;SACxB;QACD,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QACjE,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,oCAAoC;KACjD;IACD;QACE,IAAI,EAAE,kDAAkD;QACxD,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,EAAE;QACT,aAAa,EAAE,CAAC,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACpD,WAAW,EAAE;YACX,IAAI,EAAE,aAAa;SACpB;QACD,eAAe,EAAE,OAAO;QACxB,YAAY,EAAE,IAAA,WAAI,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;QACjD,UAAU,EAAE,oCAAoC;KACjD;CACF,CAAC"}