{"name": "superstruct", "type": "module", "description": "A simple and composable way to validate data in JavaScript (and TypeScript).", "version": "0.15.5", "license": "MIT", "repository": "git://github.com/ianstormtaylor/superstruct.git", "source": "./src/index.ts", "types": "./lib/index.d.ts", "main": "./lib/index.cjs", "module": "./lib/index.es.js", "exports": {"import": "./lib/index.es.js", "require": "./lib/index.cjs"}, "sideEffects": false, "files": ["umd", "lib"], "publishConfig": {"registry": "https://registry.npmjs.org"}, "devDependencies": {"@babel/cli": "^7.6.3", "@babel/core": "^7.6.3", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@babel/register": "^7.6.2", "@types/lodash": "^4.14.144", "@types/mocha": "^9.0.0", "@types/node": "^17.0.21", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "babel-eslint": "^10.0.3", "eslint": "^7.14.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^4.0.0", "is-email": "^1.0.0", "is-url": "^1.2.4", "is-uuid": "^1.0.2", "lodash": "^4.17.15", "mocha": "^10.0.0", "np": "^7.0.0", "prettier": "^2.0.5", "rollup": "^2.12.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts": "^3.0.1", "typescript": "^4.1.2"}, "scripts": {"build": "yarn build:types && yarn build:es && yarn build:cjs && yarn build:max && yarn build:min", "build:cjs": "rollup --config ./config/rollup-cjs.js", "build:es": "rollup --config ./config/rollup.js", "build:max": "rollup --config ./config/rollup-umd.js", "build:min": "rollup --config ./config/rollup-umd-min.js", "build:types": "tsc --emitDeclarationOnly --declarationMap --outDir ./lib", "clean": "rm -rf ./{lib,umd,node_modules}", "fix": "yarn fix:eslint && yarn fix:prettier", "fix:eslint": "yarn lint:eslint --fix", "fix:prettier": "yarn lint:prettier --write", "lint": "yarn lint:eslint && yarn lint:prettier", "lint:eslint": "eslint '{src,test}/*.{js,ts}'", "lint:prettier": "prettier --list-different '**/*.{js,json,ts}'", "release": "yarn build && yarn lint && np", "test": "yarn build:types && yarn test:types && yarn build:cjs && yarn test:mocha", "test:mocha": "mocha --require ./test/register.cjs --require source-map-support/register ./test/index.ts", "test:types": "tsc --noEmit && tsc --project ./test/tsconfig.json --noEmit", "watch": "yarn build:cjs --watch", "watch:types": "yarn build:types --watch"}, "keywords": ["api", "array", "assert", "cast", "check", "checker", "collection", "data", "error", "express", "hapi", "interface", "invalid", "joi", "json", "list", "model", "object", "orm", "scalar", "schema", "struct", "throw", "type", "types", "valid", "validate", "validation", "validator"], "dependencies": {}}