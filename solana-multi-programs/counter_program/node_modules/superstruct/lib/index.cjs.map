{"version": 3, "file": "index.cjs", "sources": ["../src/error.ts", "../src/utils.ts", "../src/struct.ts", "../src/structs/utilities.ts", "../src/structs/types.ts", "../src/structs/coercions.ts", "../src/structs/refinements.ts"], "sourcesContent": ["/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n\nexport type Failure = {\n  value: any\n  key: any\n  type: string\n  refinement: string | undefined\n  message: string\n  branch: Array<any>\n  path: Array<any>\n}\n\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\n\nexport class StructError extends TypeError {\n  value: any\n  key!: any\n  type!: string\n  refinement!: string | undefined\n  path!: Array<any>\n  branch!: Array<any>\n  failures: () => Array<Failure>;\n  [x: string]: any\n\n  constructor(failure: Failure, failures: () => Generator<Failure>) {\n    let cached: Array<Failure> | undefined\n    const { message, ...rest } = failure\n    const { path } = failure\n    const msg =\n      path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`\n    super(msg)\n    Object.assign(this, rest)\n    this.name = this.constructor.name\n    this.failures = () => {\n      return (cached ??= [failure, ...failures()])\n    }\n  }\n}\n", "import { <PERSON>ruct, Infer, Result, Context, Describe } from './struct'\nimport { Failure } from './error'\n\n/**\n * Check if a value is an iterator.\n */\n\nfunction isIterable<T>(x: unknown): x is Iterable<T> {\n  return isObject(x) && typeof x[Symbol.iterator] === 'function'\n}\n\n/**\n * Check if a value is a plain object.\n */\n\nexport function isObject(x: unknown): x is object {\n  return typeof x === 'object' && x != null\n}\n\n/**\n * Check if a value is a plain object.\n */\n\nexport function isPlainObject(x: unknown): x is { [key: string]: any } {\n  if (Object.prototype.toString.call(x) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(x)\n  return prototype === null || prototype === Object.prototype\n}\n\n/**\n * Return a value as a printable string.\n */\n\nexport function print(value: any): string {\n  return typeof value === 'string' ? JSON.stringify(value) : `${value}`\n}\n\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\n\nexport function shiftIterator<T>(input: Iterator<T>): T | undefined {\n  const { done, value } = input.next()\n  return done ? undefined : value\n}\n\n/**\n * Convert a single validation result to a failure.\n */\n\nexport function toFailure<T, S>(\n  result: string | boolean | Partial<Failure>,\n  context: Context,\n  struct: Struct<T, S>,\n  value: any\n): Failure | undefined {\n  if (result === true) {\n    return\n  } else if (result === false) {\n    result = {}\n  } else if (typeof result === 'string') {\n    result = { message: result }\n  }\n\n  const { path, branch } = context\n  const { type } = struct\n  const {\n    refinement,\n    message = `Expected a value of type \\`${type}\\`${\n      refinement ? ` with refinement \\`${refinement}\\`` : ''\n    }, but received: \\`${print(value)}\\``,\n  } = result\n\n  return {\n    value,\n    type,\n    refinement,\n    key: path[path.length - 1],\n    path,\n    branch,\n    ...result,\n    message,\n  }\n}\n\n/**\n * Convert a validation result to an iterable of failures.\n */\n\nexport function* toFailures<T, S>(\n  result: Result,\n  context: Context,\n  struct: Struct<T, S>,\n  value: any\n): IterableIterator<Failure> {\n  if (!isIterable(result)) {\n    result = [result]\n  }\n\n  for (const r of result) {\n    const failure = toFailure(r, context, struct, value)\n\n    if (failure) {\n      yield failure\n    }\n  }\n}\n\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\n\nexport function* run<T, S>(\n  value: unknown,\n  struct: Struct<T, S>,\n  options: {\n    path?: any[]\n    branch?: any[]\n    coerce?: boolean\n    mask?: boolean\n  } = {}\n): IterableIterator<[Failure, undefined] | [undefined, T]> {\n  const { path = [], branch = [value], coerce = false, mask = false } = options\n  const ctx: Context = { path, branch }\n\n  if (coerce) {\n    value = struct.coercer(value, ctx)\n\n    if (\n      mask &&\n      struct.type !== 'type' &&\n      isObject(struct.schema) &&\n      isObject(value) &&\n      !Array.isArray(value)\n    ) {\n      for (const key in value) {\n        if (struct.schema[key] === undefined) {\n          delete value[key]\n        }\n      }\n    }\n  }\n\n  let valid = true\n\n  for (const failure of struct.validator(value, ctx)) {\n    valid = false\n    yield [failure, undefined]\n  }\n\n  for (let [k, v, s] of struct.entries(value, ctx)) {\n    const ts = run(v, s as Struct, {\n      path: k === undefined ? path : [...path, k],\n      branch: k === undefined ? branch : [...branch, v],\n      coerce,\n      mask,\n    })\n\n    for (const t of ts) {\n      if (t[0]) {\n        valid = false\n        yield [t[0], undefined]\n      } else if (coerce) {\n        v = t[1]\n\n        if (k === undefined) {\n          value = v\n        } else if (value instanceof Map) {\n          value.set(k, v)\n        } else if (value instanceof Set) {\n          value.add(v)\n        } else if (isObject(value)) {\n          value[k] = v\n        }\n      }\n    }\n  }\n\n  if (valid) {\n    for (const failure of struct.refiner(value as T, ctx)) {\n      valid = false\n      yield [failure, undefined]\n    }\n  }\n\n  if (valid) {\n    yield [undefined, value as T]\n  }\n}\n\n/**\n * Convert a union of type to an intersection.\n */\n\nexport type UnionToIntersection<U> = (\n  U extends any ? (arg: U) => any : never\n) extends (arg: infer I) => void\n  ? I\n  : never\n\n/**\n * Assign properties from one type to another, overwriting existing.\n */\n\nexport type Assign<T, U> = Simplify<U & Omit<T, keyof U>>\n\n/**\n * A schema for enum structs.\n */\n\nexport type EnumSchema<T extends string | number | undefined> = {\n  [K in NonNullable<T>]: K\n}\n\n/**\n * Check if a type is a match for another whilst treating overlapping\n * unions as a match.\n */\n\nexport type IsMatch<T, G> = T extends G ? (G extends T ? T : never) : never\n\n/**\n * Check if a type is an exact match.\n */\n\nexport type IsExactMatch<T, U> = (<G>() => G extends T ? 1 : 2) extends <\n  G\n>() => G extends U ? 1 : 2\n  ? T\n  : never\n\n/**\n * Check if a type is a record type.\n */\n\nexport type IsRecord<T> = T extends object\n  ? string extends keyof T\n    ? T\n    : never\n  : never\n/**\n * Check if a type is a tuple.\n */\n\nexport type IsTuple<T> = T extends [any]\n  ? T\n  : T extends [any, any]\n  ? T\n  : T extends [any, any, any]\n  ? T\n  : T extends [any, any, any, any]\n  ? T\n  : T extends [any, any, any, any, any]\n  ? T\n  : never\n\n/**\n * Check if a type is a union.\n */\n\nexport type IsUnion<T, U extends T = T> = (\n  T extends any ? (U extends T ? false : true) : false\n) extends false\n  ? never\n  : T\n\n/**\n * A schema for object structs.\n */\n\nexport type ObjectSchema = Record<string, Struct<any, any>>\n\n/**\n * Infer a type from an object struct schema.\n */\n\nexport type ObjectType<S extends ObjectSchema> = Simplify<\n  Optionalize<{ [K in keyof S]: Infer<S[K]> }>\n>\n\n/**\n * Omit properties from a type that extend from a specific type.\n */\n\nexport type OmitBy<T, V> = Omit<\n  T,\n  { [K in keyof T]: V extends Extract<T[K], V> ? K : never }[keyof T]\n>\n\n/**\n * Normalize properties of a type that allow `undefined` to make them optional.\n */\n\nexport type Optionalize<S extends object> = OmitBy<S, undefined> &\n  Partial<PickBy<S, undefined>>\n\n/**\n * Transform an object schema type to represent a partial.\n */\n\nexport type PartialObjectSchema<S extends ObjectSchema> = {\n  [K in keyof S]: Struct<Infer<S[K]> | undefined>\n}\n\n/**\n * Pick properties from a type that extend from a specific type.\n */\n\nexport type PickBy<T, V> = Pick<\n  T,\n  { [K in keyof T]: V extends Extract<T[K], V> ? K : never }[keyof T]\n>\n\n/**\n * Simplifies a type definition to its most basic representation.\n */\n\nexport type Simplify<T> = T extends any[] | Date\n  ? T\n  : { [K in keyof T]: T[K] } & {}\n\nexport type If<B extends Boolean, Then, Else> = B extends true ? Then : Else\n\n/**\n * A schema for any type of struct.\n */\n\nexport type StructSchema<T> = [T] extends [string | undefined]\n  ? [T] extends [IsMatch<T, string | undefined>]\n    ? null\n    : [T] extends [IsUnion<T>]\n    ? EnumSchema<T>\n    : T\n  : [T] extends [number | undefined]\n  ? [T] extends [IsMatch<T, number | undefined>]\n    ? null\n    : [T] extends [IsUnion<T>]\n    ? EnumSchema<T>\n    : T\n  : [T] extends [boolean]\n  ? [T] extends [IsExactMatch<T, boolean>]\n    ? null\n    : T\n  : T extends\n      | bigint\n      | symbol\n      | undefined\n      | null\n      | Function\n      | Date\n      | Error\n      | RegExp\n      | Map<any, any>\n      | WeakMap<any, any>\n      | Set<any>\n      | WeakSet<any>\n      | Promise<any>\n  ? null\n  : T extends Array<infer E>\n  ? T extends IsTuple<T>\n    ? null\n    : Struct<E>\n  : T extends object\n  ? T extends IsRecord<T>\n    ? null\n    : { [K in keyof T]: Describe<T[K]> }\n  : null\n\n/**\n * A schema for tuple structs.\n */\n\nexport type TupleSchema<T> = { [K in keyof T]: Struct<T[K]> }\n\n/**\n * Shorthand type for matching any `Struct`.\n */\n\nexport type AnyStruct = Struct<any, any>\n\n/**\n * Infer a tuple of types from a tuple of `Struct`s.\n *\n * This is used to recursively retrieve the type from `union` `intersection` and\n * `tuple` structs.\n */\n\nexport type InferStructTuple<\n  Tuple extends AnyStruct[],\n  Length extends number = Tuple['length']\n> = Length extends Length\n  ? number extends Length\n    ? Tuple\n    : _InferTuple<Tuple, Length, []>\n  : never\ntype _InferTuple<\n  Tuple extends AnyStruct[],\n  Length extends number,\n  Accumulated extends unknown[],\n  Index extends number = Accumulated['length']\n> = Index extends Length\n  ? Accumulated\n  : _InferTuple<Tuple, Length, [...Accumulated, Infer<Tuple[Index]>]>\n", "import { toFailures, shiftIterator, StructSchema, run } from './utils'\nimport { StructError, Failure } from './error'\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\n\nexport class Struct<T = unknown, S = unknown> {\n  readonly TYPE!: T\n  type: string\n  schema: S\n  coercer: (value: unknown, context: Context) => unknown\n  validator: (value: unknown, context: Context) => Iterable<Failure>\n  refiner: (value: T, context: Context) => Iterable<Failure>\n  entries: (\n    value: unknown,\n    context: Context\n  ) => Iterable<[string | number, unknown, Struct<any> | Struct<never>]>\n\n  constructor(props: {\n    type: string\n    schema: S\n    coercer?: Coercer\n    validator?: Validator\n    refiner?: Refiner<T>\n    entries?: Struct<T, S>['entries']\n  }) {\n    const {\n      type,\n      schema,\n      validator,\n      refiner,\n      coercer = (value: unknown) => value,\n      entries = function* () {},\n    } = props\n\n    this.type = type\n    this.schema = schema\n    this.entries = entries\n    this.coercer = coercer\n\n    if (validator) {\n      this.validator = (value, context) => {\n        const result = validator(value, context)\n        return toFailures(result, context, this, value)\n      }\n    } else {\n      this.validator = () => []\n    }\n\n    if (refiner) {\n      this.refiner = (value, context) => {\n        const result = refiner(value, context)\n        return toFailures(result, context, this, value)\n      }\n    } else {\n      this.refiner = () => []\n    }\n  }\n\n  /**\n   * Assert that a value passes the struct's validation, throwing if it doesn't.\n   */\n\n  assert(value: unknown): asserts value is T {\n    return assert(value, this)\n  }\n\n  /**\n   * Create a value with the struct's coercion logic, then validate it.\n   */\n\n  create(value: unknown): T {\n    return create(value, this)\n  }\n\n  /**\n   * Check if a value passes the struct's validation.\n   */\n\n  is(value: unknown): value is T {\n    return is(value, this)\n  }\n\n  /**\n   * Mask a value, coercing and validating it, but returning only the subset of\n   * properties defined by the struct's schema.\n   */\n\n  mask(value: unknown): T {\n    return mask(value, this)\n  }\n\n  /**\n   * Validate a value with the struct's validation logic, returning a tuple\n   * representing the result.\n   *\n   * You may optionally pass `true` for the `withCoercion` argument to coerce\n   * the value before attempting to validate it. If you do, the result will\n   * contain the coerced result when successful.\n   */\n\n  validate(\n    value: unknown,\n    options: {\n      coerce?: boolean\n    } = {}\n  ): [StructError, undefined] | [undefined, T] {\n    return validate(value, this, options)\n  }\n}\n\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\n\nexport function assert<T, S>(\n  value: unknown,\n  struct: Struct<T, S>\n): asserts value is T {\n  const result = validate(value, struct)\n\n  if (result[0]) {\n    throw result[0]\n  }\n}\n\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\n\nexport function create<T, S>(value: unknown, struct: Struct<T, S>): T {\n  const result = validate(value, struct, { coerce: true })\n\n  if (result[0]) {\n    throw result[0]\n  } else {\n    return result[1]\n  }\n}\n\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\n\nexport function mask<T, S>(value: unknown, struct: Struct<T, S>): T {\n  const result = validate(value, struct, { coerce: true, mask: true })\n\n  if (result[0]) {\n    throw result[0]\n  } else {\n    return result[1]\n  }\n}\n\n/**\n * Check if a value passes a struct.\n */\n\nexport function is<T, S>(value: unknown, struct: Struct<T, S>): value is T {\n  const result = validate(value, struct)\n  return !result[0]\n}\n\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\n\nexport function validate<T, S>(\n  value: unknown,\n  struct: Struct<T, S>,\n  options: {\n    coerce?: boolean\n    mask?: boolean\n  } = {}\n): [StructError, undefined] | [undefined, T] {\n  const tuples = run(value, struct, options)\n  const tuple = shiftIterator(tuples)!\n\n  if (tuple[0]) {\n    const error = new StructError(tuple[0], function* () {\n      for (const t of tuples) {\n        if (t[0]) {\n          yield t[0]\n        }\n      }\n    })\n\n    return [error, undefined]\n  } else {\n    const v = tuple[1]\n    return [undefined, v]\n  }\n}\n\n/**\n * A `Context` contains information about the current location of the\n * validation inside the initial input value.\n */\n\nexport type Context = {\n  branch: Array<any>\n  path: Array<any>\n}\n\n/**\n * A type utility to extract the type from a `Struct` class.\n */\n\nexport type Infer<T extends Struct<any, any>> = T['TYPE']\n\n/**\n * A type utility to describe that a struct represents a TypeScript type.\n */\n\nexport type Describe<T> = Struct<T, StructSchema<T>>\n\n/**\n * A `Result` is returned from validation functions.\n */\n\nexport type Result =\n  | boolean\n  | string\n  | Partial<Failure>\n  | Iterable<boolean | string | Partial<Failure>>\n\n/**\n * A `Coercer` takes an unknown value and optionally coerces it.\n */\n\nexport type Coercer<T = unknown> = (value: T, context: Context) => unknown\n\n/**\n * A `Validator` takes an unknown value and validates it.\n */\n\nexport type Validator = (value: unknown, context: Context) => Result\n\n/**\n * A `Refiner` takes a value of a known type and validates it against a further\n * constraint.\n */\n\nexport type Refiner<T> = (value: T, context: Context) => Result\n", "import { Struct, Context, Validator } from '../struct'\nimport { object, optional, type } from './types'\nimport { ObjectSchema, Assign, ObjectType, PartialObjectSchema } from '../utils'\n\n/**\n * Create a new struct that combines the properties properties from multiple\n * object or type structs. Its return type will match the first parameter's type.\n *\n * Like JavaScript's `Object.assign` utility.\n */\n\nexport function assign<A extends ObjectSchema, B extends ObjectSchema>(\n  A: Struct<ObjectType<A>, A>,\n  B: Struct<ObjectType<B>, B>\n): Struct<ObjectType<Assign<A, B>>, Assign<A, B>>\nexport function assign<\n  A extends ObjectSchema,\n  B extends ObjectSchema,\n  C extends ObjectSchema\n>(\n  A: Struct<ObjectType<A>, A>,\n  B: Struct<ObjectType<B>, B>,\n  C: Struct<ObjectType<C>, C>\n): Struct<ObjectType<Assign<Assign<A, B>, C>>, Assign<Assign<A, B>, C>>\nexport function assign<\n  A extends ObjectSchema,\n  B extends ObjectSchema,\n  C extends ObjectSchema,\n  D extends ObjectSchema\n>(\n  A: Struct<ObjectType<A>, A>,\n  B: Struct<ObjectType<B>, B>,\n  C: Struct<ObjectType<C>, C>,\n  D: Struct<ObjectType<D>, D>\n): Struct<\n  ObjectType<Assign<Assign<Assign<A, B>, C>, D>>,\n  Assign<Assign<Assign<A, B>, C>, D>\n>\nexport function assign<\n  A extends ObjectSchema,\n  B extends ObjectSchema,\n  C extends ObjectSchema,\n  D extends ObjectSchema,\n  E extends ObjectSchema\n>(\n  A: Struct<ObjectType<A>, A>,\n  B: Struct<ObjectType<B>, B>,\n  C: Struct<ObjectType<C>, C>,\n  D: Struct<ObjectType<D>, D>,\n  E: Struct<ObjectType<E>, E>\n): Struct<\n  ObjectType<Assign<Assign<Assign<Assign<A, B>, C>, D>, E>>,\n  Assign<Assign<Assign<Assign<A, B>, C>, D>, E>\n>\nexport function assign(...Structs: Struct<any>[]): any {\n  const isType = Structs[0].type === 'type'\n  const schemas = Structs.map((s) => s.schema)\n  const schema = Object.assign({}, ...schemas)\n  return isType ? type(schema) : object(schema)\n}\n\n/**\n * Define a new struct type with a custom validation function.\n */\n\nexport function define<T>(name: string, validator: Validator): Struct<T, null> {\n  return new Struct({ type: name, schema: null, validator })\n}\n\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\n\nexport function deprecated<T>(\n  struct: Struct<T>,\n  log: (value: unknown, ctx: Context) => void\n): Struct<T> {\n  return new Struct({\n    ...struct,\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n    validator(value, ctx) {\n      if (value === undefined) {\n        return true\n      } else {\n        log(value, ctx)\n        return struct.validator(value, ctx)\n      }\n    },\n  })\n}\n\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\n\nexport function dynamic<T>(\n  fn: (value: unknown, ctx: Context) => Struct<T, any>\n): Struct<T, null> {\n  return new Struct({\n    type: 'dynamic',\n    schema: null,\n    *entries(value, ctx) {\n      const struct = fn(value, ctx)\n      yield* struct.entries(value, ctx)\n    },\n    validator(value, ctx) {\n      const struct = fn(value, ctx)\n      return struct.validator(value, ctx)\n    },\n    coercer(value, ctx) {\n      const struct = fn(value, ctx)\n      return struct.coercer(value, ctx)\n    },\n    refiner(value, ctx) {\n      const struct = fn(value, ctx)\n      return struct.refiner(value, ctx)\n    },\n  })\n}\n\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\n\nexport function lazy<T>(fn: () => Struct<T, any>): Struct<T, null> {\n  let struct: Struct<T, any> | undefined\n  return new Struct({\n    type: 'lazy',\n    schema: null,\n    *entries(value, ctx) {\n      struct ??= fn()\n      yield* struct.entries(value, ctx)\n    },\n    validator(value, ctx) {\n      struct ??= fn()\n      return struct.validator(value, ctx)\n    },\n    coercer(value, ctx) {\n      struct ??= fn()\n      return struct.coercer(value, ctx)\n    },\n    refiner(value, ctx) {\n      struct ??= fn()\n      return struct.refiner(value, ctx)\n    },\n  })\n}\n\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\n\nexport function omit<S extends ObjectSchema, K extends keyof S>(\n  struct: Struct<ObjectType<S>, S>,\n  keys: K[]\n): Struct<ObjectType<Omit<S, K>>, Omit<S, K>> {\n  const { schema } = struct\n  const subschema: any = { ...schema }\n\n  for (const key of keys) {\n    delete subschema[key]\n  }\n\n  switch (struct.type) {\n    case 'type':\n      return type(subschema as Omit<S, K>)\n    default:\n      return object(subschema as Omit<S, K>)\n  }\n}\n\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\n\nexport function partial<S extends ObjectSchema>(\n  struct: Struct<ObjectType<S>, S> | S\n): Struct<ObjectType<PartialObjectSchema<S>>, PartialObjectSchema<S>> {\n  const schema: any =\n    struct instanceof Struct ? { ...struct.schema } : { ...struct }\n\n  for (const key in schema) {\n    schema[key] = optional(schema[key])\n  }\n\n  return object(schema) as any\n}\n\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\n\nexport function pick<S extends ObjectSchema, K extends keyof S>(\n  struct: Struct<ObjectType<S>, S>,\n  keys: K[]\n): Struct<ObjectType<Pick<S, K>>, Pick<S, K>> {\n  const { schema } = struct\n  const subschema: any = {}\n\n  for (const key of keys) {\n    subschema[key] = schema[key]\n  }\n\n  return object(subschema as Pick<S, K>)\n}\n\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\n\nexport function struct<T>(name: string, validator: Validator): Struct<T, null> {\n  console.warn(\n    'superstruct@0.11 - The `struct` helper has been renamed to `define`.'\n  )\n\n  return define(name, validator)\n}\n", "import { Infer, Struct } from '../struct'\nimport { define } from './utilities'\nimport {\n  ObjectSchema,\n  ObjectType,\n  print,\n  run,\n  isObject,\n  AnyStruct,\n  InferStructTuple,\n  UnionToIntersection,\n} from '../utils'\n\n/**\n * Ensure that any value passes validation.\n */\n\nexport function any(): Struct<any, null> {\n  return define('any', () => true)\n}\n\n/**\n * Ensure that a value is an array and that its elements are of a specific type.\n *\n * Note: If you omit the element struct, the arrays elements will not be\n * iterated at all. This can be helpful for cases where performance is critical,\n * and it is preferred to using `array(any())`.\n */\n\nexport function array<T extends Struct<any>>(Element: T): Struct<Infer<T>[], T>\nexport function array(): Struct<unknown[], undefined>\nexport function array<T extends Struct<any>>(Element?: T): any {\n  return new Struct({\n    type: 'array',\n    schema: Element,\n    *entries(value) {\n      if (Element && Array.isArray(value)) {\n        for (const [i, v] of value.entries()) {\n          yield [i, v, Element]\n        }\n      }\n    },\n    coercer(value) {\n      return Array.isArray(value) ? value.slice() : value\n    },\n    validator(value) {\n      return (\n        Array.isArray(value) ||\n        `Expected an array value, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value is a bigint.\n */\n\nexport function bigint(): Struct<bigint, null> {\n  return define('bigint', (value) => {\n    return typeof value === 'bigint'\n  })\n}\n\n/**\n * Ensure that a value is a boolean.\n */\n\nexport function boolean(): Struct<boolean, null> {\n  return define('boolean', (value) => {\n    return typeof value === 'boolean'\n  })\n}\n\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\n\nexport function date(): Struct<Date, null> {\n  return define('date', (value) => {\n    return (\n      (value instanceof Date && !isNaN(value.getTime())) ||\n      `Expected a valid \\`Date\\` object, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value is one of a set of potential values.\n *\n * Note: after creating the struct, you can access the definition of the\n * potential values as `struct.schema`.\n */\n\nexport function enums<T extends number>(\n  values: readonly T[]\n): Struct<T, { [K in T[][number]]: K }>\nexport function enums<T extends string>(\n  values: readonly T[]\n): Struct<T, { [K in T[][number]]: K }>\nexport function enums<T extends number | string>(values: readonly T[]): any {\n  const schema: any = {}\n  const description = values.map((v) => print(v)).join()\n\n  for (const key of values) {\n    schema[key] = key\n  }\n\n  return new Struct({\n    type: 'enums',\n    schema,\n    validator(value) {\n      return (\n        values.includes(value as any) ||\n        `Expected one of \\`${description}\\`, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value is a function.\n */\n\nexport function func(): Struct<Function, null> {\n  return define('func', (value) => {\n    return (\n      typeof value === 'function' ||\n      `Expected a function, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value is an instance of a specific class.\n */\n\nexport function instance<T extends { new (...args: any): any }>(\n  Class: T\n): Struct<InstanceType<T>, null> {\n  return define('instance', (value) => {\n    return (\n      value instanceof Class ||\n      `Expected a \\`${Class.name}\\` instance, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value is an integer.\n */\n\nexport function integer(): Struct<number, null> {\n  return define('integer', (value) => {\n    return (\n      (typeof value === 'number' && !isNaN(value) && Number.isInteger(value)) ||\n      `Expected an integer, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value matches all of a set of types.\n */\n\nexport function intersection<A extends AnyStruct, B extends AnyStruct[]>(\n  Structs: [A, ...B]\n): Struct<Infer<A> & UnionToIntersection<InferStructTuple<B>[number]>, null> {\n  return new Struct({\n    type: 'intersection',\n    schema: null,\n    *entries(value, ctx) {\n      for (const S of Structs) {\n        yield* S.entries(value, ctx)\n      }\n    },\n    *validator(value, ctx) {\n      for (const S of Structs) {\n        yield* S.validator(value, ctx)\n      }\n    },\n    *refiner(value, ctx) {\n      for (const S of Structs) {\n        yield* S.refiner(value, ctx)\n      }\n    },\n  })\n}\n\n/**\n * Ensure that a value is an exact value, using `===` for comparison.\n */\n\nexport function literal<T extends boolean>(constant: T): Struct<T, T>\nexport function literal<T extends number>(constant: T): Struct<T, T>\nexport function literal<T extends string>(constant: T): Struct<T, T>\nexport function literal<T>(constant: T): Struct<T, null>\nexport function literal<T>(constant: T): any {\n  const description = print(constant)\n  const t = typeof constant\n  return new Struct({\n    type: 'literal',\n    schema:\n      t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n    validator(value) {\n      return (\n        value === constant ||\n        `Expected the literal \\`${description}\\`, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value is a `Map` object, and that its keys and values are of\n * specific types.\n */\n\nexport function map(): Struct<Map<unknown, unknown>, null>\nexport function map<K, V>(\n  Key: Struct<K>,\n  Value: Struct<V>\n): Struct<Map<K, V>, null>\nexport function map<K, V>(Key?: Struct<K>, Value?: Struct<V>): any {\n  return new Struct({\n    type: 'map',\n    schema: null,\n    *entries(value) {\n      if (Key && Value && value instanceof Map) {\n        for (const [k, v] of value.entries()) {\n          yield [k as string, k, Key]\n          yield [k as string, v, Value]\n        }\n      }\n    },\n    coercer(value) {\n      return value instanceof Map ? new Map(value) : value\n    },\n    validator(value) {\n      return (\n        value instanceof Map ||\n        `Expected a \\`Map\\` object, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that no value ever passes validation.\n */\n\nexport function never(): Struct<never, null> {\n  return define('never', () => false)\n}\n\n/**\n * Augment an existing struct to allow `null` values.\n */\n\nexport function nullable<T, S>(struct: Struct<T, S>): Struct<T | null, S> {\n  return new Struct({\n    ...struct,\n    validator: (value, ctx) => value === null || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === null || struct.refiner(value, ctx),\n  })\n}\n\n/**\n * Ensure that a value is a number.\n */\n\nexport function number(): Struct<number, null> {\n  return define('number', (value) => {\n    return (\n      (typeof value === 'number' && !isNaN(value)) ||\n      `Expected a number, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value is an object, that is has a known set of properties,\n * and that its properties are of specific types.\n *\n * Note: Unrecognized properties will fail validation.\n */\n\nexport function object(): Struct<Record<string, unknown>, null>\nexport function object<S extends ObjectSchema>(\n  schema: S\n): Struct<ObjectType<S>, S>\nexport function object<S extends ObjectSchema>(schema?: S): any {\n  const knowns = schema ? Object.keys(schema) : []\n  const Never = never()\n  return new Struct({\n    type: 'object',\n    schema: schema ? schema : null,\n    *entries(value) {\n      if (schema && isObject(value)) {\n        const unknowns = new Set(Object.keys(value))\n\n        for (const key of knowns) {\n          unknowns.delete(key)\n          yield [key, value[key], schema[key]]\n        }\n\n        for (const key of unknowns) {\n          yield [key, value[key], Never]\n        }\n      }\n    },\n    validator(value) {\n      return (\n        isObject(value) || `Expected an object, but received: ${print(value)}`\n      )\n    },\n    coercer(value) {\n      return isObject(value) ? { ...value } : value\n    },\n  })\n}\n\n/**\n * Augment a struct to allow `undefined` values.\n */\n\nexport function optional<T, S>(struct: Struct<T, S>): Struct<T | undefined, S> {\n  return new Struct({\n    ...struct,\n    validator: (value, ctx) =>\n      value === undefined || struct.validator(value, ctx),\n    refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n  })\n}\n\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\n\nexport function record<K extends string, V>(\n  Key: Struct<K>,\n  Value: Struct<V>\n): Struct<Record<K, V>, null> {\n  return new Struct({\n    type: 'record',\n    schema: null,\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k in value) {\n          const v = value[k]\n          yield [k, k, Key]\n          yield [k, v, Value]\n        }\n      }\n    },\n    validator(value) {\n      return (\n        isObject(value) || `Expected an object, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\n\nexport function regexp(): Struct<RegExp, null> {\n  return define('regexp', (value) => {\n    return value instanceof RegExp\n  })\n}\n\n/**\n * Ensure that a value is a `Set` object, and that its elements are of a\n * specific type.\n */\n\nexport function set(): Struct<Set<unknown>, null>\nexport function set<T>(Element: Struct<T>): Struct<Set<T>, null>\nexport function set<T>(Element?: Struct<T>): any {\n  return new Struct({\n    type: 'set',\n    schema: null,\n    *entries(value) {\n      if (Element && value instanceof Set) {\n        for (const v of value) {\n          yield [v as string, v, Element]\n        }\n      }\n    },\n    coercer(value) {\n      return value instanceof Set ? new Set(value) : value\n    },\n    validator(value) {\n      return (\n        value instanceof Set ||\n        `Expected a \\`Set\\` object, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value is a string.\n */\n\nexport function string(): Struct<string, null> {\n  return define('string', (value) => {\n    return (\n      typeof value === 'string' ||\n      `Expected a string, but received: ${print(value)}`\n    )\n  })\n}\n\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\n\nexport function tuple<A extends AnyStruct, B extends AnyStruct[]>(\n  Structs: [A, ...B]\n): Struct<[Infer<A>, ...InferStructTuple<B>], null> {\n  const Never = never()\n\n  return new Struct({\n    type: 'tuple',\n    schema: null,\n    *entries(value) {\n      if (Array.isArray(value)) {\n        const length = Math.max(Structs.length, value.length)\n\n        for (let i = 0; i < length; i++) {\n          yield [i, value[i], Structs[i] || Never]\n        }\n      }\n    },\n    validator(value) {\n      return (\n        Array.isArray(value) ||\n        `Expected an array, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\n\nexport function type<S extends ObjectSchema>(\n  schema: S\n): Struct<ObjectType<S>, S> {\n  const keys = Object.keys(schema)\n  return new Struct({\n    type: 'type',\n    schema,\n    *entries(value) {\n      if (isObject(value)) {\n        for (const k of keys) {\n          yield [k, value[k], schema[k]]\n        }\n      }\n    },\n    validator(value) {\n      return (\n        isObject(value) || `Expected an object, but received: ${print(value)}`\n      )\n    },\n  })\n}\n\n/**\n * Ensure that a value matches one of a set of types.\n */\n\nexport function union<A extends AnyStruct, B extends AnyStruct[]>(\n  Structs: [A, ...B]\n): Struct<Infer<A> | InferStructTuple<B>[number], null> {\n  const description = Structs.map((s) => s.type).join(' | ')\n  return new Struct({\n    type: 'union',\n    schema: null,\n    coercer(value, ctx) {\n      const firstMatch =\n        Structs.find((s) => {\n          const [e] = s.validate(value, { coerce: true })\n          return !e\n        }) || unknown()\n      return firstMatch.coercer(value, ctx)\n    },\n    validator(value, ctx) {\n      const failures = []\n\n      for (const S of Structs) {\n        const [...tuples] = run(value, S, ctx)\n        const [first] = tuples\n\n        if (!first[0]) {\n          return []\n        } else {\n          for (const [failure] of tuples) {\n            if (failure) {\n              failures.push(failure)\n            }\n          }\n        }\n      }\n\n      return [\n        `Expected the value to satisfy a union of \\`${description}\\`, but received: ${print(\n          value\n        )}`,\n        ...failures,\n      ]\n    },\n  })\n}\n\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\n\nexport function unknown(): Struct<unknown, null> {\n  return define('unknown', () => true)\n}\n", "import { Struct, is, Coer<PERSON> } from '../struct'\nimport { isPlainObject } from '../utils'\nimport { string, unknown } from './types'\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nexport function coerce<T, S, C>(\n  struct: Struct<T, S>,\n  condition: Struct<C, any>,\n  coercer: Coercer<C>\n): Struct<T, S> {\n  return new Struct({\n    ...struct,\n    coercer: (value, ctx) => {\n      return is(value, condition)\n        ? struct.coercer(coercer(value, ctx), ctx)\n        : struct.coercer(value, ctx)\n    },\n  })\n}\n\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nexport function defaulted<T, S>(\n  struct: Struct<T, S>,\n  fallback: any,\n  options: {\n    strict?: boolean\n  } = {}\n): Struct<T, S> {\n  return coerce(struct, unknown(), (x) => {\n    const f = typeof fallback === 'function' ? fallback() : fallback\n\n    if (x === undefined) {\n      return f\n    }\n\n    if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n      const ret = { ...x }\n      let changed = false\n\n      for (const key in f) {\n        if (ret[key] === undefined) {\n          ret[key] = f[key]\n          changed = true\n        }\n      }\n\n      if (changed) {\n        return ret\n      }\n    }\n\n    return x\n  })\n}\n\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\n\nexport function trimmed<T, S>(struct: Struct<T, S>): Struct<T, S> {\n  return coerce(struct, string(), (x) => x.trim())\n}\n", "import { Struct, Refiner } from '../struct'\nimport { toFailures } from '../utils'\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\n\nexport function empty<\n  T extends string | any[] | Map<any, any> | Set<any>,\n  S extends any\n>(struct: Struct<T, S>): Struct<T, S> {\n  return refine(struct, 'empty', (value) => {\n    const size = getSize(value)\n    return (\n      size === 0 ||\n      `Expected an empty ${struct.type} but received one with a size of \\`${size}\\``\n    )\n  })\n}\n\nfunction getSize(value: string | any[] | Map<any, any> | Set<any>): number {\n  if (value instanceof Map || value instanceof Set) {\n    return value.size\n  } else {\n    return value.length\n  }\n}\n\n/**\n * Ensure that a number or date is below a threshold.\n */\n\nexport function max<T extends number | Date, S extends any>(\n  struct: Struct<T, S>,\n  threshold: T,\n  options: {\n    exclusive?: boolean\n  } = {}\n): Struct<T, S> {\n  const { exclusive } = options\n  return refine(struct, 'max', (value) => {\n    return exclusive\n      ? value < threshold\n      : value <= threshold ||\n          `Expected a ${struct.type} less than ${\n            exclusive ? '' : 'or equal to '\n          }${threshold} but received \\`${value}\\``\n  })\n}\n\n/**\n * Ensure that a number or date is above a threshold.\n */\n\nexport function min<T extends number | Date, S extends any>(\n  struct: Struct<T, S>,\n  threshold: T,\n  options: {\n    exclusive?: boolean\n  } = {}\n): Struct<T, S> {\n  const { exclusive } = options\n  return refine(struct, 'min', (value) => {\n    return exclusive\n      ? value > threshold\n      : value >= threshold ||\n          `Expected a ${struct.type} greater than ${\n            exclusive ? '' : 'or equal to '\n          }${threshold} but received \\`${value}\\``\n  })\n}\n\n/**\n * Ensure that a string, array, map or set is not empty.\n */\n\nexport function nonempty<\n  T extends string | any[] | Map<any, any> | Set<any>,\n  S extends any\n>(struct: Struct<T, S>): Struct<T, S> {\n  return refine(struct, 'nonempty', (value) => {\n    const size = getSize(value)\n    return (\n      size > 0 || `Expected a nonempty ${struct.type} but received an empty one`\n    )\n  })\n}\n\n/**\n * Ensure that a string matches a regular expression.\n */\n\nexport function pattern<T extends string, S extends any>(\n  struct: Struct<T, S>,\n  regexp: RegExp\n): Struct<T, S> {\n  return refine(struct, 'pattern', (value) => {\n    return (\n      regexp.test(value) ||\n      `Expected a ${struct.type} matching \\`/${regexp.source}/\\` but received \"${value}\"`\n    )\n  })\n}\n\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\n\nexport function size<\n  T extends string | number | Date | any[] | Map<any, any> | Set<any>,\n  S extends any\n>(struct: Struct<T, S>, min: number, max: number = min): Struct<T, S> {\n  const expected = `Expected a ${struct.type}`\n  const of = min === max ? `of \\`${min}\\`` : `between \\`${min}\\` and \\`${max}\\``\n\n  return refine(struct, 'size', (value) => {\n    if (typeof value === 'number' || value instanceof Date) {\n      return (\n        (min <= value && value <= max) ||\n        `${expected} ${of} but received \\`${value}\\``\n      )\n    } else if (value instanceof Map || value instanceof Set) {\n      const { size } = value\n      return (\n        (min <= size && size <= max) ||\n        `${expected} with a size ${of} but received one with a size of \\`${size}\\``\n      )\n    } else {\n      const { length } = value as string | any[]\n      return (\n        (min <= length && length <= max) ||\n        `${expected} with a length ${of} but received one with a length of \\`${length}\\``\n      )\n    }\n  })\n}\n\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\n\nexport function refine<T, S>(\n  struct: Struct<T, S>,\n  name: string,\n  refiner: Refiner<T>\n): Struct<T, S> {\n  return new Struct({\n    ...struct,\n    *refiner(value, ctx) {\n      yield* struct.refiner(value, ctx)\n      const result = refiner(value, ctx)\n      const failures = toFailures(result, ctx, struct, value)\n\n      for (const failure of failures) {\n        yield { ...failure, refinement: name }\n      }\n    },\n  })\n}\n"], "names": ["StructError", "TypeError", "constructor", "failure", "failures", "cached", "message", "rest", "path", "msg", "length", "join", "value", "key", "type", "refinement", "branch", "Object", "assign", "name", "isIterable", "x", "isObject", "Symbol", "iterator", "isPlainObject", "prototype", "toString", "call", "getPrototypeOf", "print", "JSON", "stringify", "shiftIterator", "input", "done", "next", "undefined", "toFailure", "result", "context", "struct", "toFailures", "r", "run", "options", "coerce", "mask", "ctx", "coercer", "schema", "Array", "isArray", "valid", "validator", "k", "v", "s", "entries", "ts", "t", "Map", "set", "Set", "add", "refiner", "Struct", "props", "TYPE", "assert", "create", "is", "validate", "tuples", "tuple", "error", "Structs", "isType", "schemas", "map", "object", "define", "deprecated", "log", "dynamic", "fn", "lazy", "omit", "keys", "subschema", "partial", "optional", "pick", "console", "warn", "any", "array", "Element", "i", "slice", "bigint", "boolean", "date", "Date", "isNaN", "getTime", "enums", "values", "description", "includes", "func", "instance", "Class", "integer", "Number", "isInteger", "intersection", "S", "literal", "constant", "Key", "Value", "never", "nullable", "number", "knowns", "Never", "unknowns", "delete", "record", "regexp", "RegExp", "string", "Math", "max", "union", "firstMatch", "find", "e", "unknown", "first", "push", "condition", "defaulted", "fallback", "f", "strict", "ret", "changed", "trimmed", "trim", "empty", "refine", "size", "getSize", "threshold", "exclusive", "min", "nonempty", "pattern", "test", "source", "expected", "of"], "mappings": ";;;;AAAA;;AAEG;;AAYH;;;;;;;AAOG;AAEG,MAAOA,WAAP,SAA2BC,SAA3B,CAAoC;AAUxCC,EAAAA,WAAY,CAAAC,OAAA,EAAkBC,QAAlB,EAAoD;AAC9D,IAAA,IAAIC,MAAJ,CAAA;IACA,MAAM;MAAEC,OAAF;MAAW,GAAGC,IAAAA;AAAd,KAAA,GAAuBJ,OAA7B,CAAA;IACA,MAAM;AAAEK,MAAAA,IAAAA;AAAF,KAAA,GAAWL,OAAjB,CAAA;AACA,IAAA,MAAMM,GAAG,GACPD,IAAI,CAACE,MAAL,KAAgB,CAAhB,GAAoBJ,OAApB,GAAA,WAAA,GAA0CE,IAAI,CAACG,IAAL,CAAU,GAAV,CAA1C,YAA+DL,OADjE,CAAA;AAEA,IAAA,KAAA,CAAMG,GAAN,CAAA,CAAA;AAN8D,IAAA,IAAA,CAThEG,KASgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CARhEC,GAQgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAPhEC,IAOgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CANhEC,UAMgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CALhEP,IAKgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAJhEQ,MAIgE,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAHhEZ,QAGgE,GAAA,KAAA,CAAA,CAAA;AAO9Da,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBX,IAApB,CAAA,CAAA;AACA,IAAA,IAAA,CAAKY,IAAL,GAAY,IAAKjB,CAAAA,WAAL,CAAiBiB,IAA7B,CAAA;;IACA,IAAKf,CAAAA,QAAL,GAAgB,MAAK;AAAA,MAAA,IAAA,OAAA,CAAA;;MACnB,OAAQC,CAAAA,OAAAA,GAAAA,MAAR,KAAQA,IAAAA,GAAAA,OAAAA,GAAAA,MAAR,GAAmB,CAACF,OAAD,EAAU,GAAGC,QAAQ,EAArB,CAAnB,CAAA;KADF,CAAA;AAGD,GAAA;;AAtBuC;;ACpB1C;;AAEG;AAEH,SAASgB,UAAT,CAAuBC,CAAvB,EAAiC;AAC/B,EAAA,OAAOC,QAAQ,CAACD,CAAD,CAAR,IAAe,OAAOA,CAAC,CAACE,MAAM,CAACC,QAAR,CAAR,KAA8B,UAApD,CAAA;AACD,CAAA;AAED;;AAEG;;;AAEG,SAAUF,QAAV,CAAmBD,CAAnB,EAA6B;AACjC,EAAA,OAAO,OAAOA,CAAP,KAAa,QAAb,IAAyBA,CAAC,IAAI,IAArC,CAAA;AACD,CAAA;AAED;;AAEG;;AAEG,SAAUI,aAAV,CAAwBJ,CAAxB,EAAkC;EACtC,IAAIJ,MAAM,CAACS,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BP,CAA/B,CAAsC,KAAA,iBAA1C,EAA6D;AAC3D,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,MAAMK,SAAS,GAAGT,MAAM,CAACY,cAAP,CAAsBR,CAAtB,CAAlB,CAAA;EACA,OAAOK,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAKT,MAAM,CAACS,SAAlD,CAAA;AACD,CAAA;AAED;;AAEG;;AAEG,SAAUI,KAAV,CAAgBlB,KAAhB,EAA0B;AAC9B,EAAA,OAAO,OAAOA,KAAP,KAAiB,QAAjB,GAA4BmB,IAAI,CAACC,SAAL,CAAepB,KAAf,CAA5B,GAAA,EAAA,GAAuDA,KAA9D,CAAA;AACD,CAAA;AAED;;;AAGG;;AAEG,SAAUqB,aAAV,CAA2BC,KAA3B,EAA6C;EACjD,MAAM;IAAEC,IAAF;AAAQvB,IAAAA,KAAAA;GAAUsB,GAAAA,KAAK,CAACE,IAAN,EAAxB,CAAA;AACA,EAAA,OAAOD,IAAI,GAAGE,SAAH,GAAezB,KAA1B,CAAA;AACD,CAAA;AAED;;AAEG;;AAEG,SAAU0B,SAAV,CACJC,MADI,EAEJC,OAFI,EAGJC,MAHI,EAIJ7B,KAJI,EAIM;EAEV,IAAI2B,MAAM,KAAK,IAAf,EAAqB;AACnB,IAAA,OAAA;AACD,GAFD,MAEO,IAAIA,MAAM,KAAK,KAAf,EAAsB;AAC3BA,IAAAA,MAAM,GAAG,EAAT,CAAA;AACD,GAFM,MAEA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AACrCA,IAAAA,MAAM,GAAG;AAAEjC,MAAAA,OAAO,EAAEiC,MAAAA;KAApB,CAAA;AACD,GAAA;;EAED,MAAM;IAAE/B,IAAF;AAAQQ,IAAAA,MAAAA;AAAR,GAAA,GAAmBwB,OAAzB,CAAA;EACA,MAAM;AAAE1B,IAAAA,IAAAA;AAAF,GAAA,GAAW2B,MAAjB,CAAA;EACA,MAAM;IACJ1B,UADI;AAEJT,IAAAA,OAAO,GAAiCQ,4BAAAA,GAAAA,IAAjC,GACLC,GAAAA,IAAAA,UAAU,GAAyBA,oBAAAA,GAAAA,UAAzB,GAA0C,GAAA,GAAA,EAD/C,CAEce,GAAAA,mBAAAA,GAAAA,KAAK,CAAClB,KAAD,CAFnB,GAAA,GAAA;AAFH,GAAA,GAKF2B,MALJ,CAAA;EAOA,OAAO;IACL3B,KADK;IAELE,IAFK;IAGLC,UAHK;IAILF,GAAG,EAAEL,IAAI,CAACA,IAAI,CAACE,MAAL,GAAc,CAAf,CAJJ;IAKLF,IALK;IAMLQ,MANK;AAOL,IAAA,GAAGuB,MAPE;AAQLjC,IAAAA,OAAAA;GARF,CAAA;AAUD,CAAA;AAED;;AAEG;;AAEG,UAAWoC,UAAX,CACJH,MADI,EAEJC,OAFI,EAGJC,MAHI,EAIJ7B,KAJI,EAIM;AAEV,EAAA,IAAI,CAACQ,UAAU,CAACmB,MAAD,CAAf,EAAyB;IACvBA,MAAM,GAAG,CAACA,MAAD,CAAT,CAAA;AACD,GAAA;;AAED,EAAA,KAAK,MAAMI,CAAX,IAAgBJ,MAAhB,EAAwB;IACtB,MAAMpC,OAAO,GAAGmC,SAAS,CAACK,CAAD,EAAIH,OAAJ,EAAaC,MAAb,EAAqB7B,KAArB,CAAzB,CAAA;;AAEA,IAAA,IAAIT,OAAJ,EAAa;AACX,MAAA,MAAMA,OAAN,CAAA;AACD,KAAA;AACF,GAAA;AACF,CAAA;AAED;;;AAGG;;AAEG,UAAWyC,GAAX,CACJhC,KADI,EAEJ6B,MAFI,EAGJI,OAHI,EAQE;AAAA,EAAA,IALNA,OAKM,KAAA,KAAA,CAAA,EAAA;AALNA,IAAAA,OAKM,GAAF,EAAE,CAAA;AAAA,GAAA;;EAEN,MAAM;AAAErC,IAAAA,IAAI,GAAG,EAAT;IAAaQ,MAAM,GAAG,CAACJ,KAAD,CAAtB;AAA+BkC,IAAAA,MAAM,GAAG,KAAxC;AAA+CC,IAAAA,IAAI,GAAG,KAAA;AAAtD,GAAA,GAAgEF,OAAtE,CAAA;AACA,EAAA,MAAMG,GAAG,GAAY;IAAExC,IAAF;AAAQQ,IAAAA,MAAAA;GAA7B,CAAA;;AAEA,EAAA,IAAI8B,MAAJ,EAAY;IACVlC,KAAK,GAAG6B,MAAM,CAACQ,OAAP,CAAerC,KAAf,EAAsBoC,GAAtB,CAAR,CAAA;;IAEA,IACED,IAAI,IACJN,MAAM,CAAC3B,IAAP,KAAgB,MADhB,IAEAQ,QAAQ,CAACmB,MAAM,CAACS,MAAR,CAFR,IAGA5B,QAAQ,CAACV,KAAD,CAHR,IAIA,CAACuC,KAAK,CAACC,OAAN,CAAcxC,KAAd,CALH,EAME;AACA,MAAA,KAAK,MAAMC,GAAX,IAAkBD,KAAlB,EAAyB;AACvB,QAAA,IAAI6B,MAAM,CAACS,MAAP,CAAcrC,GAAd,CAAA,KAAuBwB,SAA3B,EAAsC;UACpC,OAAOzB,KAAK,CAACC,GAAD,CAAZ,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;;EAED,IAAIwC,KAAK,GAAG,IAAZ,CAAA;;EAEA,KAAK,MAAMlD,OAAX,IAAsBsC,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAAtB,EAAoD;AAClDK,IAAAA,KAAK,GAAG,KAAR,CAAA;AACA,IAAA,MAAM,CAAClD,OAAD,EAAUkC,SAAV,CAAN,CAAA;AACD,GAAA;;AAED,EAAA,KAAK,IAAI,CAACkB,CAAD,EAAIC,CAAJ,EAAOC,CAAP,CAAT,IAAsBhB,MAAM,CAACiB,OAAP,CAAe9C,KAAf,EAAsBoC,GAAtB,CAAtB,EAAkD;AAChD,IAAA,MAAMW,EAAE,GAAGf,GAAG,CAACY,CAAD,EAAIC,CAAJ,EAAiB;AAC7BjD,MAAAA,IAAI,EAAE+C,CAAC,KAAKlB,SAAN,GAAkB7B,IAAlB,GAAyB,CAAC,GAAGA,IAAJ,EAAU+C,CAAV,CADF;AAE7BvC,MAAAA,MAAM,EAAEuC,CAAC,KAAKlB,SAAN,GAAkBrB,MAAlB,GAA2B,CAAC,GAAGA,MAAJ,EAAYwC,CAAZ,CAFN;MAG7BV,MAH6B;AAI7BC,MAAAA,IAAAA;AAJ6B,KAAjB,CAAd,CAAA;;AAOA,IAAA,KAAK,MAAMa,CAAX,IAAgBD,EAAhB,EAAoB;AAClB,MAAA,IAAIC,CAAC,CAAC,CAAD,CAAL,EAAU;AACRP,QAAAA,KAAK,GAAG,KAAR,CAAA;AACA,QAAA,MAAM,CAACO,CAAC,CAAC,CAAD,CAAF,EAAOvB,SAAP,CAAN,CAAA;OAFF,MAGO,IAAIS,MAAJ,EAAY;AACjBU,QAAAA,CAAC,GAAGI,CAAC,CAAC,CAAD,CAAL,CAAA;;QAEA,IAAIL,CAAC,KAAKlB,SAAV,EAAqB;AACnBzB,UAAAA,KAAK,GAAG4C,CAAR,CAAA;AACD,SAFD,MAEO,IAAI5C,KAAK,YAAYiD,GAArB,EAA0B;AAC/BjD,UAAAA,KAAK,CAACkD,GAAN,CAAUP,CAAV,EAAaC,CAAb,CAAA,CAAA;AACD,SAFM,MAEA,IAAI5C,KAAK,YAAYmD,GAArB,EAA0B;UAC/BnD,KAAK,CAACoD,GAAN,CAAUR,CAAV,CAAA,CAAA;AACD,SAFM,MAEA,IAAIlC,QAAQ,CAACV,KAAD,CAAZ,EAAqB;AAC1BA,UAAAA,KAAK,CAAC2C,CAAD,CAAL,GAAWC,CAAX,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;;AAED,EAAA,IAAIH,KAAJ,EAAW;IACT,KAAK,MAAMlD,OAAX,IAAsBsC,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAA2BoC,GAA3B,CAAtB,EAAuD;AACrDK,MAAAA,KAAK,GAAG,KAAR,CAAA;AACA,MAAA,MAAM,CAAClD,OAAD,EAAUkC,SAAV,CAAN,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,IAAIgB,KAAJ,EAAW;AACT,IAAA,MAAM,CAAChB,SAAD,EAAYzB,KAAZ,CAAN,CAAA;AACD,GAAA;AACF;;AC9LD;;;;AAIG;;MAEUsD,OAAM;EAYjBhE,WAAA,CAAYiE,KAAZ,EAOC;AAAA,IAAA,IAAA,CAlBQC,IAkBR,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAjBDtD,IAiBC,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAhBDoC,MAgBC,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAfDD,OAeC,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAdDK,SAcC,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAbDW,OAaC,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAZDP,OAYC,GAAA,KAAA,CAAA,CAAA;IACC,MAAM;MACJ5C,IADI;MAEJoC,MAFI;MAGJI,SAHI;MAIJW,OAJI;MAKJhB,OAAO,GAAIrC,KAAD,IAAoBA,KAL1B;MAMJ8C,OAAO,GAAG,aAAS,EAAM;AANrB,KAAA,GAOFS,KAPJ,CAAA;IASA,IAAKrD,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKoC,CAAAA,MAAL,GAAcA,MAAd,CAAA;IACA,IAAKQ,CAAAA,OAAL,GAAeA,OAAf,CAAA;IACA,IAAKT,CAAAA,OAAL,GAAeA,OAAf,CAAA;;AAEA,IAAA,IAAIK,SAAJ,EAAe;AACb,MAAA,IAAA,CAAKA,SAAL,GAAiB,CAAC1C,KAAD,EAAQ4B,OAAR,KAAmB;AAClC,QAAA,MAAMD,MAAM,GAAGe,SAAS,CAAC1C,KAAD,EAAQ4B,OAAR,CAAxB,CAAA;QACA,OAAOE,UAAU,CAACH,MAAD,EAASC,OAAT,EAAkB,IAAlB,EAAwB5B,KAAxB,CAAjB,CAAA;OAFF,CAAA;AAID,KALD,MAKO;MACL,IAAK0C,CAAAA,SAAL,GAAiB,MAAM,EAAvB,CAAA;AACD,KAAA;;AAED,IAAA,IAAIW,OAAJ,EAAa;AACX,MAAA,IAAA,CAAKA,OAAL,GAAe,CAACrD,KAAD,EAAQ4B,OAAR,KAAmB;AAChC,QAAA,MAAMD,MAAM,GAAG0B,OAAO,CAACrD,KAAD,EAAQ4B,OAAR,CAAtB,CAAA;QACA,OAAOE,UAAU,CAACH,MAAD,EAASC,OAAT,EAAkB,IAAlB,EAAwB5B,KAAxB,CAAjB,CAAA;OAFF,CAAA;AAID,KALD,MAKO;MACL,IAAKqD,CAAAA,OAAL,GAAe,MAAM,EAArB,CAAA;AACD,KAAA;AACF,GAAA;AAED;;AAEG;;;EAEHI,MAAM,CAACzD,KAAD,EAAe;AACnB,IAAA,OAAOyD,MAAM,CAACzD,KAAD,EAAQ,IAAR,CAAb,CAAA;AACD,GAAA;AAED;;AAEG;;;EAEH0D,MAAM,CAAC1D,KAAD,EAAe;AACnB,IAAA,OAAO0D,MAAM,CAAC1D,KAAD,EAAQ,IAAR,CAAb,CAAA;AACD,GAAA;AAED;;AAEG;;;EAEH2D,EAAE,CAAC3D,KAAD,EAAe;AACf,IAAA,OAAO2D,EAAE,CAAC3D,KAAD,EAAQ,IAAR,CAAT,CAAA;AACD,GAAA;AAED;;;AAGG;;;EAEHmC,IAAI,CAACnC,KAAD,EAAe;AACjB,IAAA,OAAOmC,IAAI,CAACnC,KAAD,EAAQ,IAAR,CAAX,CAAA;AACD,GAAA;AAED;;;;;;;AAOG;;;AAEH4D,EAAAA,QAAQ,CACN5D,KADM,EAENiC,OAFM,EAIA;AAAA,IAAA,IAFNA,OAEM,KAAA,KAAA,CAAA,EAAA;AAFNA,MAAAA,OAEM,GAAF,EAAE,CAAA;AAAA,KAAA;;AAEN,IAAA,OAAO2B,QAAQ,CAAC5D,KAAD,EAAQ,IAAR,EAAciC,OAAd,CAAf,CAAA;AACD,GAAA;;AAtGgB,CAAA;AAyGnB;;AAEG;;AAEa,SAAAwB,MAAA,CACdzD,KADc,EAEd6B,MAFc,EAEM;AAEpB,EAAA,MAAMF,MAAM,GAAGiC,QAAQ,CAAC5D,KAAD,EAAQ6B,MAAR,CAAvB,CAAA;;AAEA,EAAA,IAAIF,MAAM,CAAC,CAAD,CAAV,EAAe;IACb,MAAMA,MAAM,CAAC,CAAD,CAAZ,CAAA;AACD,GAAA;AACF,CAAA;AAED;;AAEG;;AAEa,SAAA+B,MAAA,CAAa1D,KAAb,EAA6B6B,MAA7B,EAAiD;AAC/D,EAAA,MAAMF,MAAM,GAAGiC,QAAQ,CAAC5D,KAAD,EAAQ6B,MAAR,EAAgB;AAAEK,IAAAA,MAAM,EAAE,IAAA;AAAV,GAAhB,CAAvB,CAAA;;AAEA,EAAA,IAAIP,MAAM,CAAC,CAAD,CAAV,EAAe;IACb,MAAMA,MAAM,CAAC,CAAD,CAAZ,CAAA;AACD,GAFD,MAEO;IACL,OAAOA,MAAM,CAAC,CAAD,CAAb,CAAA;AACD,GAAA;AACF,CAAA;AAED;;AAEG;;AAEa,SAAAQ,IAAA,CAAWnC,KAAX,EAA2B6B,MAA3B,EAA+C;AAC7D,EAAA,MAAMF,MAAM,GAAGiC,QAAQ,CAAC5D,KAAD,EAAQ6B,MAAR,EAAgB;AAAEK,IAAAA,MAAM,EAAE,IAAV;AAAgBC,IAAAA,IAAI,EAAE,IAAA;AAAtB,GAAhB,CAAvB,CAAA;;AAEA,EAAA,IAAIR,MAAM,CAAC,CAAD,CAAV,EAAe;IACb,MAAMA,MAAM,CAAC,CAAD,CAAZ,CAAA;AACD,GAFD,MAEO;IACL,OAAOA,MAAM,CAAC,CAAD,CAAb,CAAA;AACD,GAAA;AACF,CAAA;AAED;;AAEG;;AAEa,SAAAgC,EAAA,CAAS3D,KAAT,EAAyB6B,MAAzB,EAA6C;AAC3D,EAAA,MAAMF,MAAM,GAAGiC,QAAQ,CAAC5D,KAAD,EAAQ6B,MAAR,CAAvB,CAAA;AACA,EAAA,OAAO,CAACF,MAAM,CAAC,CAAD,CAAd,CAAA;AACD,CAAA;AAED;;;AAGG;;AAEG,SAAUiC,QAAV,CACJ5D,KADI,EAEJ6B,MAFI,EAGJI,OAHI,EAME;AAAA,EAAA,IAHNA,OAGM,KAAA,KAAA,CAAA,EAAA;AAHNA,IAAAA,OAGM,GAAF,EAAE,CAAA;AAAA,GAAA;;EAEN,MAAM4B,MAAM,GAAG7B,GAAG,CAAChC,KAAD,EAAQ6B,MAAR,EAAgBI,OAAhB,CAAlB,CAAA;AACA,EAAA,MAAM6B,KAAK,GAAGzC,aAAa,CAACwC,MAAD,CAA3B,CAAA;;AAEA,EAAA,IAAIC,KAAK,CAAC,CAAD,CAAT,EAAc;IACZ,MAAMC,KAAK,GAAG,IAAI3E,WAAJ,CAAgB0E,KAAK,CAAC,CAAD,CAArB,EAA0B,aAAS;AAC/C,MAAA,KAAK,MAAMd,CAAX,IAAgBa,MAAhB,EAAwB;AACtB,QAAA,IAAIb,CAAC,CAAC,CAAD,CAAL,EAAU;UACR,MAAMA,CAAC,CAAC,CAAD,CAAP,CAAA;AACD,SAAA;AACF,OAAA;AACF,KANa,CAAd,CAAA;AAQA,IAAA,OAAO,CAACe,KAAD,EAAQtC,SAAR,CAAP,CAAA;AACD,GAVD,MAUO;AACL,IAAA,MAAMmB,CAAC,GAAGkB,KAAK,CAAC,CAAD,CAAf,CAAA;AACA,IAAA,OAAO,CAACrC,SAAD,EAAYmB,CAAZ,CAAP,CAAA;AACD,GAAA;AACF;;AC9Ie,SAAAtC,MAAA,GAAgC;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAAtB0D,OAAsB,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAAtBA,OAAsB,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;;EAC9C,MAAMC,MAAM,GAAGD,OAAO,CAAC,CAAD,CAAP,CAAW9D,IAAX,KAAoB,MAAnC,CAAA;EACA,MAAMgE,OAAO,GAAGF,OAAO,CAACG,GAAR,CAAatB,CAAD,IAAOA,CAAC,CAACP,MAArB,CAAhB,CAAA;EACA,MAAMA,MAAM,GAAGjC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,GAAG4D,OAArB,CAAf,CAAA;EACA,OAAOD,MAAM,GAAG/D,IAAI,CAACoC,MAAD,CAAP,GAAkB8B,MAAM,CAAC9B,MAAD,CAArC,CAAA;AACD,CAAA;AAED;;AAEG;;AAEa,SAAA+B,MAAA,CAAU9D,IAAV,EAAwBmC,SAAxB,EAA4C;EAC1D,OAAO,IAAIY,MAAJ,CAAW;AAAEpD,IAAAA,IAAI,EAAEK,IAAR;AAAc+B,IAAAA,MAAM,EAAE,IAAtB;AAA4BI,IAAAA,SAAAA;AAA5B,GAAX,CAAP,CAAA;AACD,CAAA;AAED;;;AAGG;;AAEa,SAAA4B,UAAA,CACdzC,MADc,EAEd0C,GAFc,EAE6B;AAE3C,EAAA,OAAO,IAAIjB,MAAJ,CAAW,EAChB,GAAGzB,MADa;AAEhBwB,IAAAA,OAAO,EAAE,CAACrD,KAAD,EAAQoC,GAAR,KAAgBpC,KAAK,KAAKyB,SAAV,IAAuBI,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAFhC;;AAGhBM,IAAAA,SAAS,CAAC1C,KAAD,EAAQoC,GAAR,EAAW;MAClB,IAAIpC,KAAK,KAAKyB,SAAd,EAAyB;AACvB,QAAA,OAAO,IAAP,CAAA;AACD,OAFD,MAEO;AACL8C,QAAAA,GAAG,CAACvE,KAAD,EAAQoC,GAAR,CAAH,CAAA;AACA,QAAA,OAAOP,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAAP,CAAA;AACD,OAAA;AACF,KAAA;;AAVe,GAAX,CAAP,CAAA;AAYD,CAAA;AAED;;;;;;AAMG;;AAEG,SAAUoC,OAAV,CACJC,EADI,EACgD;EAEpD,OAAO,IAAInB,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,SADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;AAGhB,IAAA,CAACQ,OAAD,CAAS9C,KAAT,EAAgBoC,GAAhB,EAAmB;AACjB,MAAA,MAAMP,MAAM,GAAG4C,EAAE,CAACzE,KAAD,EAAQoC,GAAR,CAAjB,CAAA;AACA,MAAA,OAAOP,MAAM,CAACiB,OAAP,CAAe9C,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;KALc;;AAOhBM,IAAAA,SAAS,CAAC1C,KAAD,EAAQoC,GAAR,EAAW;AAClB,MAAA,MAAMP,MAAM,GAAG4C,EAAE,CAACzE,KAAD,EAAQoC,GAAR,CAAjB,CAAA;AACA,MAAA,OAAOP,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAAP,CAAA;KATc;;AAWhBC,IAAAA,OAAO,CAACrC,KAAD,EAAQoC,GAAR,EAAW;AAChB,MAAA,MAAMP,MAAM,GAAG4C,EAAE,CAACzE,KAAD,EAAQoC,GAAR,CAAjB,CAAA;AACA,MAAA,OAAOP,MAAM,CAACQ,OAAP,CAAerC,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;KAbc;;AAehBiB,IAAAA,OAAO,CAACrD,KAAD,EAAQoC,GAAR,EAAW;AAChB,MAAA,MAAMP,MAAM,GAAG4C,EAAE,CAACzE,KAAD,EAAQoC,GAAR,CAAjB,CAAA;AACA,MAAA,OAAOP,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;AACD,KAAA;;AAlBe,GAAX,CAAP,CAAA;AAoBD,CAAA;AAED;;;;;;;AAOG;;AAEG,SAAUsC,IAAV,CAAkBD,EAAlB,EAA0C;AAC9C,EAAA,IAAI5C,MAAJ,CAAA;EACA,OAAO,IAAIyB,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,MADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;AAGhB,IAAA,CAACQ,OAAD,CAAS9C,KAAT,EAAgBoC,GAAhB,EAAmB;AAAA,MAAA,IAAA,OAAA,CAAA;;AACjB,MAAA,CAAA,OAAA,GAAAP,MAAM,KAAN,IAAA,GAAA,OAAA,GAAAA,MAAM,GAAK4C,EAAE,EAAb,CAAA;AACA,MAAA,OAAO5C,MAAM,CAACiB,OAAP,CAAe9C,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;KALc;;AAOhBM,IAAAA,SAAS,CAAC1C,KAAD,EAAQoC,GAAR,EAAW;AAAA,MAAA,IAAA,QAAA,CAAA;;AAClB,MAAA,CAAA,QAAA,GAAAP,MAAM,KAAN,IAAA,GAAA,QAAA,GAAAA,MAAM,GAAK4C,EAAE,EAAb,CAAA;AACA,MAAA,OAAO5C,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAAP,CAAA;KATc;;AAWhBC,IAAAA,OAAO,CAACrC,KAAD,EAAQoC,GAAR,EAAW;AAAA,MAAA,IAAA,QAAA,CAAA;;AAChB,MAAA,CAAA,QAAA,GAAAP,MAAM,KAAN,IAAA,GAAA,QAAA,GAAAA,MAAM,GAAK4C,EAAE,EAAb,CAAA;AACA,MAAA,OAAO5C,MAAM,CAACQ,OAAP,CAAerC,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;KAbc;;AAehBiB,IAAAA,OAAO,CAACrD,KAAD,EAAQoC,GAAR,EAAW;AAAA,MAAA,IAAA,QAAA,CAAA;;AAChB,MAAA,CAAA,QAAA,GAAAP,MAAM,KAAN,IAAA,GAAA,QAAA,GAAAA,MAAM,GAAK4C,EAAE,EAAb,CAAA;AACA,MAAA,OAAO5C,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;AACD,KAAA;;AAlBe,GAAX,CAAP,CAAA;AAoBD,CAAA;AAED;;;;;AAKG;;AAEa,SAAAuC,IAAA,CACd9C,MADc,EAEd+C,IAFc,EAEL;EAET,MAAM;AAAEtC,IAAAA,MAAAA;AAAF,GAAA,GAAaT,MAAnB,CAAA;EACA,MAAMgD,SAAS,GAAQ,EAAE,GAAGvC,MAAAA;GAA5B,CAAA;;AAEA,EAAA,KAAK,MAAMrC,GAAX,IAAkB2E,IAAlB,EAAwB;IACtB,OAAOC,SAAS,CAAC5E,GAAD,CAAhB,CAAA;AACD,GAAA;;EAED,QAAQ4B,MAAM,CAAC3B,IAAf;AACE,IAAA,KAAK,MAAL;MACE,OAAOA,IAAI,CAAC2E,SAAD,CAAX,CAAA;;AACF,IAAA;MACE,OAAOT,MAAM,CAACS,SAAD,CAAb,CAAA;AAJJ,GAAA;AAMD,CAAA;AAED;;;;;AAKG;;AAEG,SAAUC,OAAV,CACJjD,MADI,EACgC;EAEpC,MAAMS,MAAM,GACVT,MAAM,YAAYyB,MAAlB,GAA2B,EAAE,GAAGzB,MAAM,CAACS,MAAAA;GAAvC,GAAkD,EAAE,GAAGT,MAAAA;GADzD,CAAA;;AAGA,EAAA,KAAK,MAAM5B,GAAX,IAAkBqC,MAAlB,EAA0B;IACxBA,MAAM,CAACrC,GAAD,CAAN,GAAc8E,QAAQ,CAACzC,MAAM,CAACrC,GAAD,CAAP,CAAtB,CAAA;AACD,GAAA;;EAED,OAAOmE,MAAM,CAAC9B,MAAD,CAAb,CAAA;AACD,CAAA;AAED;;;;;AAKG;;AAEa,SAAA0C,IAAA,CACdnD,MADc,EAEd+C,IAFc,EAEL;EAET,MAAM;AAAEtC,IAAAA,MAAAA;AAAF,GAAA,GAAaT,MAAnB,CAAA;EACA,MAAMgD,SAAS,GAAQ,EAAvB,CAAA;;AAEA,EAAA,KAAK,MAAM5E,GAAX,IAAkB2E,IAAlB,EAAwB;AACtBC,IAAAA,SAAS,CAAC5E,GAAD,CAAT,GAAiBqC,MAAM,CAACrC,GAAD,CAAvB,CAAA;AACD,GAAA;;EAED,OAAOmE,MAAM,CAACS,SAAD,CAAb,CAAA;AACD,CAAA;AAED;;;;AAIG;;AAEa,SAAAhD,MAAA,CAAUtB,IAAV,EAAwBmC,SAAxB,EAA4C;EAC1DuC,OAAO,CAACC,IAAR,CACE,sEADF,CAAA,CAAA;AAIA,EAAA,OAAOb,MAAM,CAAC9D,IAAD,EAAOmC,SAAP,CAAb,CAAA;AACD;;AChOD;;AAEG;;SAEayC,MAAG;AACjB,EAAA,OAAOd,MAAM,CAAC,KAAD,EAAQ,MAAM,IAAd,CAAb,CAAA;AACD,CAAA;AAYK,SAAUe,KAAV,CAAuCC,OAAvC,EAAkD;EACtD,OAAO,IAAI/B,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,OADU;AAEhBoC,IAAAA,MAAM,EAAE+C,OAFQ;;IAGhB,CAACvC,OAAD,CAAS9C,KAAT,EAAc;MACZ,IAAIqF,OAAO,IAAI9C,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAAf,EAAqC;QACnC,KAAK,MAAM,CAACsF,CAAD,EAAI1C,CAAJ,CAAX,IAAqB5C,KAAK,CAAC8C,OAAN,EAArB,EAAsC;AACpC,UAAA,MAAM,CAACwC,CAAD,EAAI1C,CAAJ,EAAOyC,OAAP,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KARa;;IAUhBhD,OAAO,CAACrC,KAAD,EAAM;MACX,OAAOuC,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAAuBA,GAAAA,KAAK,CAACuF,KAAN,EAAvB,GAAuCvF,KAA9C,CAAA;KAXc;;IAahB0C,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEuC,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAC0CkB,IAAAA,yCAAAA,GAAAA,KAAK,CAAClB,KAAD,CAFjD,CAAA;AAID,KAAA;;AAlBe,GAAX,CAAP,CAAA;AAoBD,CAAA;AAED;;AAEG;;SAEawF,SAAM;AACpB,EAAA,OAAOnB,MAAM,CAAC,QAAD,EAAYrE,KAAD,IAAU;IAChC,OAAO,OAAOA,KAAP,KAAiB,QAAxB,CAAA;AACD,GAFY,CAAb,CAAA;AAGD,CAAA;AAED;;AAEG;;SAEayF,UAAO;AACrB,EAAA,OAAOpB,MAAM,CAAC,SAAD,EAAarE,KAAD,IAAU;IACjC,OAAO,OAAOA,KAAP,KAAiB,SAAxB,CAAA;AACD,GAFY,CAAb,CAAA;AAGD,CAAA;AAED;;;;;AAKG;;SAEa0F,OAAI;AAClB,EAAA,OAAOrB,MAAM,CAAC,MAAD,EAAUrE,KAAD,IAAU;AAC9B,IAAA,OACGA,KAAK,YAAY2F,IAAjB,IAAyB,CAACC,KAAK,CAAC5F,KAAK,CAAC6F,OAAN,EAAD,CAAhC,IAAA,gDAAA,GACmD3E,KAAK,CAAClB,KAAD,CAF1D,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAeK,SAAU8F,KAAV,CAA2CC,MAA3C,EAA+D;EACnE,MAAMzD,MAAM,GAAQ,EAApB,CAAA;AACA,EAAA,MAAM0D,WAAW,GAAGD,MAAM,CAAC5B,GAAP,CAAYvB,CAAD,IAAO1B,KAAK,CAAC0B,CAAD,CAAvB,CAAA,CAA4B7C,IAA5B,EAApB,CAAA;;AAEA,EAAA,KAAK,MAAME,GAAX,IAAkB8F,MAAlB,EAA0B;AACxBzD,IAAAA,MAAM,CAACrC,GAAD,CAAN,GAAcA,GAAd,CAAA;AACD,GAAA;;EAED,OAAO,IAAIqD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,OADU;IAEhBoC,MAFgB;;IAGhBI,SAAS,CAAC1C,KAAD,EAAM;MACb,OACE+F,MAAM,CAACE,QAAP,CAAgBjG,KAAhB,CACqBgG,IAAAA,mBAAAA,GAAAA,WADrB,GACqD9E,mBAAAA,GAAAA,KAAK,CAAClB,KAAD,CAF5D,CAAA;AAID,KAAA;;AARe,GAAX,CAAP,CAAA;AAUD,CAAA;AAED;;AAEG;;SAEakG,OAAI;AAClB,EAAA,OAAO7B,MAAM,CAAC,MAAD,EAAUrE,KAAD,IAAU;IAC9B,OACE,OAAOA,KAAP,KAAiB,UAAjB,4CACsCkB,KAAK,CAAClB,KAAD,CAF7C,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;AAEG;;AAEG,SAAUmG,QAAV,CACJC,KADI,EACI;AAER,EAAA,OAAO/B,MAAM,CAAC,UAAD,EAAcrE,KAAD,IAAU;IAClC,OACEA,KAAK,YAAYoG,KAAjB,IACgBA,cAAAA,GAAAA,KAAK,CAAC7F,IADtB,GACwDW,4BAAAA,GAAAA,KAAK,CAAClB,KAAD,CAF/D,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;AAEG;;SAEaqG,UAAO;AACrB,EAAA,OAAOhC,MAAM,CAAC,SAAD,EAAarE,KAAD,IAAU;IACjC,OACG,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC4F,KAAK,CAAC5F,KAAD,CAAnC,IAA8CsG,MAAM,CAACC,SAAP,CAAiBvG,KAAjB,CAA/C,IACsCkB,qCAAAA,GAAAA,KAAK,CAAClB,KAAD,CAF7C,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;AAEG;;AAEG,SAAUwG,YAAV,CACJxC,OADI,EACc;EAElB,OAAO,IAAIV,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,cADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;AAGhB,IAAA,CAACQ,OAAD,CAAS9C,KAAT,EAAgBoC,GAAhB,EAAmB;AACjB,MAAA,KAAK,MAAMqE,CAAX,IAAgBzC,OAAhB,EAAyB;AACvB,QAAA,OAAOyC,CAAC,CAAC3D,OAAF,CAAU9C,KAAV,EAAiBoC,GAAjB,CAAP,CAAA;AACD,OAAA;KANa;;AAQhB,IAAA,CAACM,SAAD,CAAW1C,KAAX,EAAkBoC,GAAlB,EAAqB;AACnB,MAAA,KAAK,MAAMqE,CAAX,IAAgBzC,OAAhB,EAAyB;AACvB,QAAA,OAAOyC,CAAC,CAAC/D,SAAF,CAAY1C,KAAZ,EAAmBoC,GAAnB,CAAP,CAAA;AACD,OAAA;KAXa;;AAahB,IAAA,CAACiB,OAAD,CAASrD,KAAT,EAAgBoC,GAAhB,EAAmB;AACjB,MAAA,KAAK,MAAMqE,CAAX,IAAgBzC,OAAhB,EAAyB;AACvB,QAAA,OAAOyC,CAAC,CAACpD,OAAF,CAAUrD,KAAV,EAAiBoC,GAAjB,CAAP,CAAA;AACD,OAAA;AACF,KAAA;;AAjBe,GAAX,CAAP,CAAA;AAmBD,CAAA;AAUK,SAAUsE,OAAV,CAAqBC,QAArB,EAAgC;AACpC,EAAA,MAAMX,WAAW,GAAG9E,KAAK,CAACyF,QAAD,CAAzB,CAAA;EACA,MAAM3D,CAAC,GAAG,OAAO2D,QAAjB,CAAA;EACA,OAAO,IAAIrD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,SADU;AAEhBoC,IAAAA,MAAM,EACJU,CAAC,KAAK,QAAN,IAAkBA,CAAC,KAAK,QAAxB,IAAoCA,CAAC,KAAK,SAA1C,GAAsD2D,QAAtD,GAAiE,IAHnD;;IAIhBjE,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEA,KAAK,KAAK2G,QAAV,IAAA,wBAAA,GAC0BX,WAD1B,GAC0D9E,mBAAAA,GAAAA,KAAK,CAAClB,KAAD,CAFjE,CAAA;AAID,KAAA;;AATe,GAAX,CAAP,CAAA;AAWD,CAAA;AAYe,SAAAmE,GAAA,CAAUyC,GAAV,EAA2BC,KAA3B,EAA4C;EAC1D,OAAO,IAAIvD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,KADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAI4G,GAAG,IAAIC,KAAP,IAAgB7G,KAAK,YAAYiD,GAArC,EAA0C;QACxC,KAAK,MAAM,CAACN,CAAD,EAAIC,CAAJ,CAAX,IAAqB5C,KAAK,CAAC8C,OAAN,EAArB,EAAsC;AACpC,UAAA,MAAM,CAACH,CAAD,EAAcA,CAAd,EAAiBiE,GAAjB,CAAN,CAAA;AACA,UAAA,MAAM,CAACjE,CAAD,EAAcC,CAAd,EAAiBiE,KAAjB,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KATa;;IAWhBxE,OAAO,CAACrC,KAAD,EAAM;MACX,OAAOA,KAAK,YAAYiD,GAAjB,GAAuB,IAAIA,GAAJ,CAAQjD,KAAR,CAAvB,GAAwCA,KAA/C,CAAA;KAZc;;IAchB0C,SAAS,CAAC1C,KAAD,EAAM;AACb,MAAA,OACEA,KAAK,YAAYiD,GAAjB,gDAC4C/B,KAAK,CAAClB,KAAD,CAFnD,CAAA;AAID,KAAA;;AAnBe,GAAX,CAAP,CAAA;AAqBD,CAAA;AAED;;AAEG;;SAEa8G,QAAK;AACnB,EAAA,OAAOzC,MAAM,CAAC,OAAD,EAAU,MAAM,KAAhB,CAAb,CAAA;AACD,CAAA;AAED;;AAEG;;AAEG,SAAU0C,QAAV,CAAyBlF,MAAzB,EAA6C;AACjD,EAAA,OAAO,IAAIyB,MAAJ,CAAW,EAChB,GAAGzB,MADa;AAEhBa,IAAAA,SAAS,EAAE,CAAC1C,KAAD,EAAQoC,GAAR,KAAgBpC,KAAK,KAAK,IAAV,IAAkB6B,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAF7B;AAGhBiB,IAAAA,OAAO,EAAE,CAACrD,KAAD,EAAQoC,GAAR,KAAgBpC,KAAK,KAAK,IAAV,IAAkB6B,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAAA;AAH3B,GAAX,CAAP,CAAA;AAKD,CAAA;AAED;;AAEG;;SAEa4E,SAAM;AACpB,EAAA,OAAO3C,MAAM,CAAC,QAAD,EAAYrE,KAAD,IAAU;AAChC,IAAA,OACG,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAAC4F,KAAK,CAAC5F,KAAD,CAApC,IAAA,mCAAA,GACoCkB,KAAK,CAAClB,KAAD,CAF3C,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAaK,SAAUoE,MAAV,CAAyC9B,MAAzC,EAAmD;EACvD,MAAM2E,MAAM,GAAG3E,MAAM,GAAGjC,MAAM,CAACuE,IAAP,CAAYtC,MAAZ,CAAH,GAAyB,EAA9C,CAAA;EACA,MAAM4E,KAAK,GAAGJ,KAAK,EAAnB,CAAA;EACA,OAAO,IAAIxD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,QADU;AAEhBoC,IAAAA,MAAM,EAAEA,MAAM,GAAGA,MAAH,GAAY,IAFV;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAIsC,MAAM,IAAI5B,QAAQ,CAACV,KAAD,CAAtB,EAA+B;QAC7B,MAAMmH,QAAQ,GAAG,IAAIhE,GAAJ,CAAQ9C,MAAM,CAACuE,IAAP,CAAY5E,KAAZ,CAAR,CAAjB,CAAA;;AAEA,QAAA,KAAK,MAAMC,GAAX,IAAkBgH,MAAlB,EAA0B;UACxBE,QAAQ,CAACC,MAAT,CAAgBnH,GAAhB,CAAA,CAAA;AACA,UAAA,MAAM,CAACA,GAAD,EAAMD,KAAK,CAACC,GAAD,CAAX,EAAkBqC,MAAM,CAACrC,GAAD,CAAxB,CAAN,CAAA;AACD,SAAA;;AAED,QAAA,KAAK,MAAMA,GAAX,IAAkBkH,QAAlB,EAA4B;UAC1B,MAAM,CAAClH,GAAD,EAAMD,KAAK,CAACC,GAAD,CAAX,EAAkBiH,KAAlB,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KAfa;;IAiBhBxE,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEU,QAAQ,CAACV,KAAD,CAAR,2CAAwDkB,KAAK,CAAClB,KAAD,CAD/D,CAAA;KAlBc;;IAsBhBqC,OAAO,CAACrC,KAAD,EAAM;AACX,MAAA,OAAOU,QAAQ,CAACV,KAAD,CAAR,GAAkB,EAAE,GAAGA,KAAAA;AAAL,OAAlB,GAAiCA,KAAxC,CAAA;AACD,KAAA;;AAxBe,GAAX,CAAP,CAAA;AA0BD,CAAA;AAED;;AAEG;;AAEG,SAAU+E,QAAV,CAAyBlD,MAAzB,EAA6C;AACjD,EAAA,OAAO,IAAIyB,MAAJ,CAAW,EAChB,GAAGzB,MADa;AAEhBa,IAAAA,SAAS,EAAE,CAAC1C,KAAD,EAAQoC,GAAR,KACTpC,KAAK,KAAKyB,SAAV,IAAuBI,MAAM,CAACa,SAAP,CAAiB1C,KAAjB,EAAwBoC,GAAxB,CAHT;AAIhBiB,IAAAA,OAAO,EAAE,CAACrD,KAAD,EAAQoC,GAAR,KAAgBpC,KAAK,KAAKyB,SAAV,IAAuBI,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAAA;AAJhC,GAAX,CAAP,CAAA;AAMD,CAAA;AAED;;;;;AAKG;;AAEa,SAAAiF,MAAA,CACdT,GADc,EAEdC,KAFc,EAEE;EAEhB,OAAO,IAAIvD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,QADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAIU,QAAQ,CAACV,KAAD,CAAZ,EAAqB;AACnB,QAAA,KAAK,MAAM2C,CAAX,IAAgB3C,KAAhB,EAAuB;AACrB,UAAA,MAAM4C,CAAC,GAAG5C,KAAK,CAAC2C,CAAD,CAAf,CAAA;AACA,UAAA,MAAM,CAACA,CAAD,EAAIA,CAAJ,EAAOiE,GAAP,CAAN,CAAA;AACA,UAAA,MAAM,CAACjE,CAAD,EAAIC,CAAJ,EAAOiE,KAAP,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KAVa;;IAYhBnE,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEU,QAAQ,CAACV,KAAD,CAAR,2CAAwDkB,KAAK,CAAClB,KAAD,CAD/D,CAAA;AAGD,KAAA;;AAhBe,GAAX,CAAP,CAAA;AAkBD,CAAA;AAED;;;;;AAKG;;SAEasH,SAAM;AACpB,EAAA,OAAOjD,MAAM,CAAC,QAAD,EAAYrE,KAAD,IAAU;IAChC,OAAOA,KAAK,YAAYuH,MAAxB,CAAA;AACD,GAFY,CAAb,CAAA;AAGD,CAAA;AASK,SAAUrE,GAAV,CAAiBmC,OAAjB,EAAoC;EACxC,OAAO,IAAI/B,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,KADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAIqF,OAAO,IAAIrF,KAAK,YAAYmD,GAAhC,EAAqC;AACnC,QAAA,KAAK,MAAMP,CAAX,IAAgB5C,KAAhB,EAAuB;AACrB,UAAA,MAAM,CAAC4C,CAAD,EAAcA,CAAd,EAAiByC,OAAjB,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KARa;;IAUhBhD,OAAO,CAACrC,KAAD,EAAM;MACX,OAAOA,KAAK,YAAYmD,GAAjB,GAAuB,IAAIA,GAAJ,CAAQnD,KAAR,CAAvB,GAAwCA,KAA/C,CAAA;KAXc;;IAahB0C,SAAS,CAAC1C,KAAD,EAAM;AACb,MAAA,OACEA,KAAK,YAAYmD,GAAjB,gDAC4CjC,KAAK,CAAClB,KAAD,CAFnD,CAAA;AAID,KAAA;;AAlBe,GAAX,CAAP,CAAA;AAoBD,CAAA;AAED;;AAEG;;SAEawH,SAAM;AACpB,EAAA,OAAOnD,MAAM,CAAC,QAAD,EAAYrE,KAAD,IAAU;IAChC,OACE,OAAOA,KAAP,KAAiB,QAAjB,0CACoCkB,KAAK,CAAClB,KAAD,CAF3C,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;;AAGG;;AAEG,SAAU8D,KAAV,CACJE,OADI,EACc;EAElB,MAAMkD,KAAK,GAAGJ,KAAK,EAAnB,CAAA;EAEA,OAAO,IAAIxD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,OADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAIuC,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAAJ,EAA0B;AACxB,QAAA,MAAMF,MAAM,GAAG2H,IAAI,CAACC,GAAL,CAAS1D,OAAO,CAAClE,MAAjB,EAAyBE,KAAK,CAACF,MAA/B,CAAf,CAAA;;QAEA,KAAK,IAAIwF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGxF,MAApB,EAA4BwF,CAAC,EAA7B,EAAiC;AAC/B,UAAA,MAAM,CAACA,CAAD,EAAItF,KAAK,CAACsF,CAAD,CAAT,EAActB,OAAO,CAACsB,CAAD,CAAP,IAAc4B,KAA5B,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KAVa;;IAYhBxE,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEuC,KAAK,CAACC,OAAN,CAAcxC,KAAd,CACoCkB,IAAAA,mCAAAA,GAAAA,KAAK,CAAClB,KAAD,CAF3C,CAAA;AAID,KAAA;;AAjBe,GAAX,CAAP,CAAA;AAmBD,CAAA;AAED;;;;;AAKG;;AAEG,SAAUE,IAAV,CACJoC,MADI,EACK;AAET,EAAA,MAAMsC,IAAI,GAAGvE,MAAM,CAACuE,IAAP,CAAYtC,MAAZ,CAAb,CAAA;EACA,OAAO,IAAIgB,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,MADU;IAEhBoC,MAFgB;;IAGhB,CAACQ,OAAD,CAAS9C,KAAT,EAAc;AACZ,MAAA,IAAIU,QAAQ,CAACV,KAAD,CAAZ,EAAqB;AACnB,QAAA,KAAK,MAAM2C,CAAX,IAAgBiC,IAAhB,EAAsB;AACpB,UAAA,MAAM,CAACjC,CAAD,EAAI3C,KAAK,CAAC2C,CAAD,CAAT,EAAcL,MAAM,CAACK,CAAD,CAApB,CAAN,CAAA;AACD,SAAA;AACF,OAAA;KARa;;IAUhBD,SAAS,CAAC1C,KAAD,EAAM;MACb,OACEU,QAAQ,CAACV,KAAD,CAAR,2CAAwDkB,KAAK,CAAClB,KAAD,CAD/D,CAAA;AAGD,KAAA;;AAde,GAAX,CAAP,CAAA;AAgBD,CAAA;AAED;;AAEG;;AAEG,SAAU2H,KAAV,CACJ3D,OADI,EACc;AAElB,EAAA,MAAMgC,WAAW,GAAGhC,OAAO,CAACG,GAAR,CAAatB,CAAD,IAAOA,CAAC,CAAC3C,IAArB,CAAA,CAA2BH,IAA3B,CAAgC,KAAhC,CAApB,CAAA;EACA,OAAO,IAAIuD,MAAJ,CAAW;AAChBpD,IAAAA,IAAI,EAAE,OADU;AAEhBoC,IAAAA,MAAM,EAAE,IAFQ;;AAGhBD,IAAAA,OAAO,CAACrC,KAAD,EAAQoC,GAAR,EAAW;AAChB,MAAA,MAAMwF,UAAU,GACd5D,OAAO,CAAC6D,IAAR,CAAchF,CAAD,IAAM;QACjB,MAAM,CAACiF,CAAD,CAAMjF,GAAAA,CAAC,CAACe,QAAF,CAAW5D,KAAX,EAAkB;AAAEkC,UAAAA,MAAM,EAAE,IAAA;AAAV,SAAlB,CAAZ,CAAA;AACA,QAAA,OAAO,CAAC4F,CAAR,CAAA;OAFF,CAAA,IAGMC,OAAO,EAJf,CAAA;AAKA,MAAA,OAAOH,UAAU,CAACvF,OAAX,CAAmBrC,KAAnB,EAA0BoC,GAA1B,CAAP,CAAA;KATc;;AAWhBM,IAAAA,SAAS,CAAC1C,KAAD,EAAQoC,GAAR,EAAW;MAClB,MAAM5C,QAAQ,GAAG,EAAjB,CAAA;;AAEA,MAAA,KAAK,MAAMiH,CAAX,IAAgBzC,OAAhB,EAAyB;QACvB,MAAM,CAAC,GAAGH,MAAJ,CAAc7B,GAAAA,GAAG,CAAChC,KAAD,EAAQyG,CAAR,EAAWrE,GAAX,CAAvB,CAAA;QACA,MAAM,CAAC4F,KAAD,CAAA,GAAUnE,MAAhB,CAAA;;AAEA,QAAA,IAAI,CAACmE,KAAK,CAAC,CAAD,CAAV,EAAe;AACb,UAAA,OAAO,EAAP,CAAA;AACD,SAFD,MAEO;AACL,UAAA,KAAK,MAAM,CAACzI,OAAD,CAAX,IAAwBsE,MAAxB,EAAgC;AAC9B,YAAA,IAAItE,OAAJ,EAAa;cACXC,QAAQ,CAACyI,IAAT,CAAc1I,OAAd,CAAA,CAAA;AACD,aAAA;AACF,WAAA;AACF,SAAA;AACF,OAAA;;MAED,OAAO,CAAA,4CAAA,GACyCyG,WADzC,GAAA,mBAAA,GACyE9E,KAAK,CACjFlB,KADiF,CAD9E,EAIL,GAAGR,QAJE,CAAP,CAAA;AAMD,KAAA;;AAnCe,GAAX,CAAP,CAAA;AAqCD,CAAA;AAED;;AAEG;;SAEauI,UAAO;AACrB,EAAA,OAAO1D,MAAM,CAAC,SAAD,EAAY,MAAM,IAAlB,CAAb,CAAA;AACD;;ACthBD;;;;;;;;;AASG;;SAEanC,OACdL,QACAqG,WACA7F,SAAmB;AAEnB,EAAA,OAAO,IAAIiB,MAAJ,CAAW,EAChB,GAAGzB,MADa;AAEhBQ,IAAAA,OAAO,EAAE,CAACrC,KAAD,EAAQoC,GAAR,KAAe;AACtB,MAAA,OAAOuB,EAAE,CAAC3D,KAAD,EAAQkI,SAAR,CAAF,GACHrG,MAAM,CAACQ,OAAP,CAAeA,OAAO,CAACrC,KAAD,EAAQoC,GAAR,CAAtB,EAAoCA,GAApC,CADG,GAEHP,MAAM,CAACQ,OAAP,CAAerC,KAAf,EAAsBoC,GAAtB,CAFJ,CAAA;AAGD,KAAA;AANe,GAAX,CAAP,CAAA;AAQD,CAAA;AAED;;;;;AAKG;;AAEG,SAAU+F,SAAV,CACJtG,MADI,EAEJuG,QAFI,EAGJnG,OAHI,EAKE;AAAA,EAAA,IAFNA,OAEM,KAAA,KAAA,CAAA,EAAA;AAFNA,IAAAA,OAEM,GAAF,EAAE,CAAA;AAAA,GAAA;;EAEN,OAAOC,MAAM,CAACL,MAAD,EAASkG,OAAO,EAAhB,EAAqBtH,CAAD,IAAM;IACrC,MAAM4H,CAAC,GAAG,OAAOD,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,EAAzC,GAA8CA,QAAxD,CAAA;;IAEA,IAAI3H,CAAC,KAAKgB,SAAV,EAAqB;AACnB,MAAA,OAAO4G,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAACpG,OAAO,CAACqG,MAAT,IAAmBzH,aAAa,CAACJ,CAAD,CAAhC,IAAuCI,aAAa,CAACwH,CAAD,CAAxD,EAA6D;MAC3D,MAAME,GAAG,GAAG,EAAE,GAAG9H,CAAAA;OAAjB,CAAA;MACA,IAAI+H,OAAO,GAAG,KAAd,CAAA;;AAEA,MAAA,KAAK,MAAMvI,GAAX,IAAkBoI,CAAlB,EAAqB;AACnB,QAAA,IAAIE,GAAG,CAACtI,GAAD,CAAH,KAAawB,SAAjB,EAA4B;AAC1B8G,UAAAA,GAAG,CAACtI,GAAD,CAAH,GAAWoI,CAAC,CAACpI,GAAD,CAAZ,CAAA;AACAuI,UAAAA,OAAO,GAAG,IAAV,CAAA;AACD,SAAA;AACF,OAAA;;AAED,MAAA,IAAIA,OAAJ,EAAa;AACX,QAAA,OAAOD,GAAP,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,OAAO9H,CAAP,CAAA;AACD,GAxBY,CAAb,CAAA;AAyBD,CAAA;AAED;;;;;AAKG;;AAEG,SAAUgI,OAAV,CAAwB5G,MAAxB,EAA4C;AAChD,EAAA,OAAOK,MAAM,CAACL,MAAD,EAAS2F,MAAM,EAAf,EAAoB/G,CAAD,IAAOA,CAAC,CAACiI,IAAF,EAA1B,CAAb,CAAA;AACD;;AC7ED;;AAEG;;AAEG,SAAUC,KAAV,CAGJ9G,MAHI,EAGgB;AACpB,EAAA,OAAO+G,MAAM,CAAC/G,MAAD,EAAS,OAAT,EAAmB7B,KAAD,IAAU;AACvC,IAAA,MAAM6I,IAAI,GAAGC,OAAO,CAAC9I,KAAD,CAApB,CAAA;IACA,OACE6I,IAAI,KAAK,CAAT,IAAA,oBAAA,GACqBhH,MAAM,CAAC3B,IAD5B,GACsE2I,oCAAAA,GAAAA,IADtE,GADF,GAAA,CAAA;AAID,GANY,CAAb,CAAA;AAOD,CAAA;;AAED,SAASC,OAAT,CAAiB9I,KAAjB,EAAiE;AAC/D,EAAA,IAAIA,KAAK,YAAYiD,GAAjB,IAAwBjD,KAAK,YAAYmD,GAA7C,EAAkD;IAChD,OAAOnD,KAAK,CAAC6I,IAAb,CAAA;AACD,GAFD,MAEO;IACL,OAAO7I,KAAK,CAACF,MAAb,CAAA;AACD,GAAA;AACF,CAAA;AAED;;AAEG;;;AAEG,SAAU4H,GAAV,CACJ7F,MADI,EAEJkH,SAFI,EAGJ9G,OAHI,EAKE;AAAA,EAAA,IAFNA,OAEM,KAAA,KAAA,CAAA,EAAA;AAFNA,IAAAA,OAEM,GAAF,EAAE,CAAA;AAAA,GAAA;;EAEN,MAAM;AAAE+G,IAAAA,SAAAA;AAAF,GAAA,GAAgB/G,OAAtB,CAAA;AACA,EAAA,OAAO2G,MAAM,CAAC/G,MAAD,EAAS,KAAT,EAAiB7B,KAAD,IAAU;IACrC,OAAOgJ,SAAS,GACZhJ,KAAK,GAAG+I,SADI,GAEZ/I,KAAK,IAAI+I,SAAT,IACgBlH,aAAAA,GAAAA,MAAM,CAAC3B,IADvB,GAAA,aAAA,IAEI8I,SAAS,GAAG,EAAH,GAAQ,cAFrB,CAGKD,GAAAA,SAHL,GAGiC/I,iBAAAA,GAAAA,KAHjC,GAFJ,GAAA,CAAA;AAMD,GAPY,CAAb,CAAA;AAQD,CAAA;AAED;;AAEG;;AAEG,SAAUiJ,GAAV,CACJpH,MADI,EAEJkH,SAFI,EAGJ9G,OAHI,EAKE;AAAA,EAAA,IAFNA,OAEM,KAAA,KAAA,CAAA,EAAA;AAFNA,IAAAA,OAEM,GAAF,EAAE,CAAA;AAAA,GAAA;;EAEN,MAAM;AAAE+G,IAAAA,SAAAA;AAAF,GAAA,GAAgB/G,OAAtB,CAAA;AACA,EAAA,OAAO2G,MAAM,CAAC/G,MAAD,EAAS,KAAT,EAAiB7B,KAAD,IAAU;IACrC,OAAOgJ,SAAS,GACZhJ,KAAK,GAAG+I,SADI,GAEZ/I,KAAK,IAAI+I,SAAT,IACgBlH,aAAAA,GAAAA,MAAM,CAAC3B,IADvB,GAAA,gBAAA,IAEI8I,SAAS,GAAG,EAAH,GAAQ,cAFrB,CAGKD,GAAAA,SAHL,GAGiC/I,iBAAAA,GAAAA,KAHjC,GAFJ,GAAA,CAAA;AAMD,GAPY,CAAb,CAAA;AAQD,CAAA;AAED;;AAEG;;AAEG,SAAUkJ,QAAV,CAGJrH,MAHI,EAGgB;AACpB,EAAA,OAAO+G,MAAM,CAAC/G,MAAD,EAAS,UAAT,EAAsB7B,KAAD,IAAU;AAC1C,IAAA,MAAM6I,IAAI,GAAGC,OAAO,CAAC9I,KAAD,CAApB,CAAA;AACA,IAAA,OACE6I,IAAI,GAAG,CAAP,6BAAmChH,MAAM,CAAC3B,IAA1C,GADF,4BAAA,CAAA;AAGD,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;AAEG;;AAEa,SAAAiJ,OAAA,CACdtH,MADc,EAEdyF,MAFc,EAEA;AAEd,EAAA,OAAOsB,MAAM,CAAC/G,MAAD,EAAS,SAAT,EAAqB7B,KAAD,IAAU;AACzC,IAAA,OACEsH,MAAM,CAAC8B,IAAP,CAAYpJ,KAAZ,CACc6B,IAAAA,aAAAA,GAAAA,MAAM,CAAC3B,IADrB,oBACyCoH,MAAM,CAAC+B,MADhD,GAAA,oBAAA,GAC2ErJ,KAD3E,GADF,IAAA,CAAA;AAID,GALY,CAAb,CAAA;AAMD,CAAA;AAED;;AAEG;;AAEG,SAAU6I,IAAV,CAGJhH,MAHI,EAGkBoH,GAHlB,EAG+BvB,GAH/B,EAGgD;AAAA,EAAA,IAAjBA,GAAiB,KAAA,KAAA,CAAA,EAAA;AAAjBA,IAAAA,GAAiB,GAAHuB,GAAG,CAAA;AAAA,GAAA;;AACpD,EAAA,MAAMK,QAAQ,GAAA,aAAA,GAAiBzH,MAAM,CAAC3B,IAAtC,CAAA;EACA,MAAMqJ,EAAE,GAAGN,GAAG,KAAKvB,GAAR,GAAsBuB,MAAAA,GAAAA,GAAtB,GAA6CA,GAAAA,GAAAA,WAAAA,GAAAA,GAA7C,GAA4DvB,SAAAA,GAAAA,GAA5D,GAAX,GAAA,CAAA;AAEA,EAAA,OAAOkB,MAAM,CAAC/G,MAAD,EAAS,MAAT,EAAkB7B,KAAD,IAAU;IACtC,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,YAAY2F,IAAlD,EAAwD;AACtD,MAAA,OACGsD,GAAG,IAAIjJ,KAAP,IAAgBA,KAAK,IAAI0H,GAA1B,IACG4B,QADH,GAAA,GAAA,GACeC,EADf,GAAA,iBAAA,GACoCvJ,KADpC,GADF,GAAA,CAAA;KADF,MAKO,IAAIA,KAAK,YAAYiD,GAAjB,IAAwBjD,KAAK,YAAYmD,GAA7C,EAAkD;MACvD,MAAM;AAAE0F,QAAAA,IAAAA;AAAF,OAAA,GAAW7I,KAAjB,CAAA;AACA,MAAA,OACGiJ,GAAG,IAAIJ,IAAP,IAAeA,IAAI,IAAInB,GAAxB,IACG4B,QADH,GAAA,eAAA,GAC2BC,EAD3B,GAAA,oCAAA,GACmEV,IADnE,GADF,GAAA,CAAA;AAID,KANM,MAMA;MACL,MAAM;AAAE/I,QAAAA,MAAAA;AAAF,OAAA,GAAaE,KAAnB,CAAA;AACA,MAAA,OACGiJ,GAAG,IAAInJ,MAAP,IAAiBA,MAAM,IAAI4H,GAA5B,IACG4B,QADH,GAAA,iBAAA,GAC6BC,EAD7B,GAAA,sCAAA,GACuEzJ,MADvE,GADF,GAAA,CAAA;AAID,KAAA;AACF,GAnBY,CAAb,CAAA;AAoBD,CAAA;AAED;;;;;;AAMG;;SAEa8I,OACd/G,QACAtB,MACA8C,SAAmB;AAEnB,EAAA,OAAO,IAAIC,MAAJ,CAAW,EAChB,GAAGzB,MADa;;AAEhB,IAAA,CAACwB,OAAD,CAASrD,KAAT,EAAgBoC,GAAhB,EAAmB;AACjB,MAAA,OAAOP,MAAM,CAACwB,OAAP,CAAerD,KAAf,EAAsBoC,GAAtB,CAAP,CAAA;AACA,MAAA,MAAMT,MAAM,GAAG0B,OAAO,CAACrD,KAAD,EAAQoC,GAAR,CAAtB,CAAA;MACA,MAAM5C,QAAQ,GAAGsC,UAAU,CAACH,MAAD,EAASS,GAAT,EAAcP,MAAd,EAAsB7B,KAAtB,CAA3B,CAAA;;AAEA,MAAA,KAAK,MAAMT,OAAX,IAAsBC,QAAtB,EAAgC;QAC9B,MAAM,EAAE,GAAGD,OAAL;AAAcY,UAAAA,UAAU,EAAEI,IAAAA;SAAhC,CAAA;AACD,OAAA;AACF,KAAA;;AAVe,GAAX,CAAP,CAAA;AAYD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}