{"version": 3, "file": "struct.d.ts", "sourceRoot": "", "sources": ["../src/struct.ts"], "names": [], "mappings": "AAAA,OAAO,EAA6B,YAAY,EAAO,MAAM,SAAS,CAAA;AACtE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAE9C;;;;GAIG;AAEH,qBAAa,MAAM,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO;IAC1C,QAAQ,CAAC,IAAI,EAAG,CAAC,CAAA;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,CAAC,CAAA;IACT,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACtD,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA;IAClE,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA;IAC1D,OAAO,EAAE,CACP,KAAK,EAAE,OAAO,EACd,OAAO,EAAE,OAAO,KACb,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAE1D,KAAK,EAAE;QACjB,IAAI,EAAE,MAAM,CAAA;QACZ,MAAM,EAAE,CAAC,CAAA;QACT,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,SAAS,CAAC,EAAE,SAAS,CAAA;QACrB,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;QACpB,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;KAClC;IAkCD;;OAEG;IAEH,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC;IAI1C;;OAEG;IAEH,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,CAAC;IAIzB;;OAEG;IAEH,EAAE,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,CAAC;IAI9B;;;OAGG;IAEH,IAAI,CAAC,KAAK,EAAE,OAAO,GAAG,CAAC;IAIvB;;;;;;;OAOG;IAEH,QAAQ,CACN,KAAK,EAAE,OAAO,EACd,OAAO,GAAE;QACP,MAAM,CAAC,EAAE,OAAO,CAAA;KACZ,GACL,CAAC,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;CAG7C;AAED;;GAEG;AAEH,wBAAgB,MAAM,CAAC,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GACnB,OAAO,CAAC,KAAK,IAAI,CAAC,CAMpB;AAED;;GAEG;AAEH,wBAAgB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAQpE;AAED;;GAEG;AAEH,wBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAQlE;AAED;;GAEG;AAEH,wBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAGzE;AAED;;;GAGG;AAEH,wBAAgB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAC3B,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,OAAO,GAAE;IACP,MAAM,CAAC,EAAE,OAAO,CAAA;IAChB,IAAI,CAAC,EAAE,OAAO,CAAA;CACV,GACL,CAAC,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAkB3C;AAED;;;GAGG;AAEH,oBAAY,OAAO,GAAG;IACpB,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;IAClB,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;CACjB,CAAA;AAED;;GAEG;AAEH,oBAAY,KAAK,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAA;AAEzD;;GAEG;AAEH,oBAAY,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;AAEpD;;GAEG;AAEH,oBAAY,MAAM,GACd,OAAO,GACP,MAAM,GACN,OAAO,CAAC,OAAO,CAAC,GAChB,QAAQ,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;AAEjD;;GAEG;AAEH,oBAAY,OAAO,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;AAE1E;;GAEG;AAEH,oBAAY,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,KAAK,MAAM,CAAA;AAEpE;;;GAGG;AAEH,oBAAY,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,KAAK,MAAM,CAAA"}