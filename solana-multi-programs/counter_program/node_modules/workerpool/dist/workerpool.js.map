{"version": 3, "file": "workerpool.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;ACVA,IAAIA,OAAO,GAAGC,mBAAO,CAAC,GAAD,CAArB;;AACA,IAAIC,aAAa,GAAGD,mBAAO,CAAC,GAAD,CAA3B;;AACA,IAAIE,WAAW,GAAGF,mBAAO,CAAC,GAAD,CAAzB;;AACA,IAAIG,kBAAkB,GAAGH,mBAAO,CAAC,GAAD,CAAhC;;AACA,IAAII,oBAAoB,GAAG,IAAID,kBAAJ,EAA3B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASE,IAAT,CAAcC,MAAd,EAAsBC,OAAtB,EAA+B;AAC7B,MAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,SAAKA,MAAL,GAAcA,MAAM,IAAI,IAAxB;AACD,GAFD,MAGK;AACH,SAAKA,MAAL,GAAc,IAAd;AACAC,IAAAA,OAAO,GAAGD,MAAV;AACD;;AAED,OAAKE,OAAL,GAAe,EAAf,CAT6B,CAST;;AACpB,OAAKC,KAAL,GAAa,EAAb,CAV6B,CAUT;;AAEpBF,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAArB;AAEA,OAAKG,QAAL,GAAgBC,MAAM,CAACC,MAAP,CAAcL,OAAO,CAACG,QAAR,IAAoB,EAAlC,CAAhB;AACA,OAAKG,QAAL,GAAgBF,MAAM,CAACC,MAAP,CAAcL,OAAO,CAACM,QAAR,IAAoB,EAAlC,CAAhB;AACA,OAAKC,cAAL,GAAuBP,OAAO,CAACO,cAAR,IAA0B,KAAjD;AACA,OAAKC,UAAL,GAAkBR,OAAO,CAACQ,UAA1B;AACA,OAAKC,UAAL,GAAkBT,OAAO,CAACS,UAAR,IAAsBT,OAAO,CAACQ,UAA9B,IAA4C,MAA9D;AACA,OAAKE,YAAL,GAAoBV,OAAO,CAACU,YAAR,IAAwBC,QAA5C;;AAEA,OAAKC,cAAL,GAAsBZ,OAAO,CAACY,cAAR,IAA2B;AAAA,WAAM,IAAN;AAAA,GAAjD;;AACA,OAAKC,iBAAL,GAAyBb,OAAO,CAACa,iBAAR,IAA8B;AAAA,WAAM,IAAN;AAAA,GAAvD,CAtB6B,CAwB7B;;;AACA,MAAIb,OAAO,IAAI,gBAAgBA,OAA/B,EAAwC;AACtCc,IAAAA,kBAAkB,CAACd,OAAO,CAACe,UAAT,CAAlB;AACA,SAAKA,UAAL,GAAkBf,OAAO,CAACe,UAA1B;AACD,GAHD,MAIK;AACH,SAAKA,UAAL,GAAkBC,IAAI,CAACC,GAAL,CAAS,CAACtB,WAAW,CAACuB,IAAZ,IAAoB,CAArB,IAA0B,CAAnC,EAAsC,CAAtC,CAAlB;AACD;;AAED,MAAIlB,OAAO,IAAI,gBAAgBA,OAA/B,EAAwC;AACtC,QAAGA,OAAO,CAACmB,UAAR,KAAuB,KAA1B,EAAiC;AAC/B,WAAKA,UAAL,GAAkB,KAAKJ,UAAvB;AACD,KAFD,MAEO;AACLK,MAAAA,kBAAkB,CAACpB,OAAO,CAACmB,UAAT,CAAlB;AACA,WAAKA,UAAL,GAAkBnB,OAAO,CAACmB,UAA1B;AACA,WAAKJ,UAAL,GAAkBC,IAAI,CAACC,GAAL,CAAS,KAAKE,UAAd,EAA0B,KAAKJ,UAA/B,CAAlB,CAHK,CAG6D;AACnE;;AACD,SAAKM,iBAAL;AACD;;AAED,OAAKC,UAAL,GAAkB,KAAKC,KAAL,CAAWC,IAAX,CAAgB,IAAhB,CAAlB;;AAGA,MAAI,KAAKf,UAAL,KAAoB,QAAxB,EAAkC;AAChCf,IAAAA,aAAa,CAAC+B,mBAAd;AACD;AACF;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA3B,IAAI,CAAC4B,SAAL,CAAeC,IAAf,GAAsB,UAAUC,MAAV,EAAkBC,MAAlB,EAA0B7B,OAA1B,EAAmC;AACvD;AACA,MAAI6B,MAAM,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAf,EAAsC;AACpC,UAAM,IAAIG,SAAJ,CAAc,qCAAd,CAAN;AACD;;AAED,MAAI,OAAOJ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAIK,QAAQ,GAAGzC,OAAO,CAAC0C,KAAR,EAAf;;AAEA,QAAI,KAAKhC,KAAL,CAAWiC,MAAX,IAAqB,KAAKzB,YAA9B,EAA4C;AAC1C,YAAM,IAAI0B,KAAJ,CAAU,uBAAuB,KAAK1B,YAA5B,GAA2C,UAArD,CAAN;AACD,KAL6B,CAO9B;;;AACA,QAAIR,KAAK,GAAG,KAAKA,KAAjB;AACA,QAAImC,IAAI,GAAG;AACTT,MAAAA,MAAM,EAAGA,MADA;AAETC,MAAAA,MAAM,EAAGA,MAFA;AAGTI,MAAAA,QAAQ,EAAEA,QAHD;AAITK,MAAAA,OAAO,EAAE,IAJA;AAKTtC,MAAAA,OAAO,EAAEA;AALA,KAAX;AAOAE,IAAAA,KAAK,CAACqC,IAAN,CAAWF,IAAX,EAhB8B,CAkB9B;AACA;;AACA,QAAIG,eAAe,GAAGP,QAAQ,CAACQ,OAAT,CAAiBH,OAAvC;;AACAL,IAAAA,QAAQ,CAACQ,OAAT,CAAiBH,OAAjB,GAA2B,SAASA,OAAT,CAAkBI,KAAlB,EAAyB;AAClD,UAAIxC,KAAK,CAACyC,OAAN,CAAcN,IAAd,MAAwB,CAAC,CAA7B,EAAgC;AAC9B;AACAA,QAAAA,IAAI,CAACC,OAAL,GAAeI,KAAf;AACA,eAAOT,QAAQ,CAACQ,OAAhB;AACD,OAJD,MAKK;AACH;AACA,eAAOD,eAAe,CAACI,IAAhB,CAAqBX,QAAQ,CAACQ,OAA9B,EAAuCC,KAAvC,CAAP;AACD;AACF,KAVD,CArB8B,CAiC9B;;;AACA,SAAKnB,KAAL;;AAEA,WAAOU,QAAQ,CAACQ,OAAhB;AACD,GArCD,MAsCK,IAAI,OAAOb,MAAP,KAAkB,UAAtB,EAAkC;AACrC;AACA,WAAO,KAAKD,IAAL,CAAU,KAAV,EAAiB,CAACkB,MAAM,CAACjB,MAAD,CAAP,EAAiBC,MAAjB,CAAjB,CAAP;AACD,GAHI,MAIA;AACH,UAAM,IAAIG,SAAJ,CAAc,kDAAd,CAAN;AACD;AACF,CAnDD;AAqDA;AACA;AACA;AACA;AACA;AACA;;;AACAlC,IAAI,CAAC4B,SAAL,CAAeoB,KAAf,GAAuB,YAAY;AACjC,MAAIC,SAAS,CAACZ,MAAV,GAAmB,CAAvB,EAA0B;AACxB,UAAM,IAAIC,KAAJ,CAAU,uBAAV,CAAN;AACD;;AAED,MAAIY,IAAI,GAAG,IAAX;AACA,SAAO,KAAKrB,IAAL,CAAU,SAAV,EACFsB,IADE,CACG,UAAUC,OAAV,EAAmB;AACvB,QAAIJ,KAAK,GAAG,EAAZ;AAEAI,IAAAA,OAAO,CAACC,OAAR,CAAgB,UAAUvB,MAAV,EAAkB;AAChCkB,MAAAA,KAAK,CAAClB,MAAD,CAAL,GAAgB,YAAY;AAC1B,eAAOoB,IAAI,CAACrB,IAAL,CAAUC,MAAV,EAAkBE,KAAK,CAACJ,SAAN,CAAgB0B,KAAhB,CAAsBR,IAAtB,CAA2BG,SAA3B,CAAlB,CAAP;AACD,OAFD;AAGD,KAJD;AAMA,WAAOD,KAAP;AACD,GAXE,CAAP;AAYD,CAlBD;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AACAhD,IAAI,CAAC4B,SAAL,CAAeH,KAAf,GAAuB,YAAY;AACjC,MAAI,KAAKrB,KAAL,CAAWiC,MAAX,GAAoB,CAAxB,EAA2B;AACzB;AAEA;AACA,QAAIkB,MAAM,GAAG,KAAKC,UAAL,EAAb;;AACA,QAAID,MAAJ,EAAY;AACV;AACA,UAAIE,EAAE,GAAG,IAAT;AACA,UAAIlB,IAAI,GAAG,KAAKnC,KAAL,CAAWsD,KAAX,EAAX,CAHU,CAKV;;AACA,UAAInB,IAAI,CAACJ,QAAL,CAAcQ,OAAd,CAAsBgB,OAA1B,EAAmC;AACjC;AACA,YAAIhB,OAAO,GAAGY,MAAM,CAAC1B,IAAP,CAAYU,IAAI,CAACT,MAAjB,EAAyBS,IAAI,CAACR,MAA9B,EAAsCQ,IAAI,CAACJ,QAA3C,EAAqDI,IAAI,CAACrC,OAA1D,EACXiD,IADW,CACNM,EAAE,CAACjC,UADG,WAEL,YAAY;AACjB;AACA,cAAI+B,MAAM,CAACK,UAAX,EAAuB;AACrB,mBAAOH,EAAE,CAACI,aAAH,CAAiBN,MAAjB,CAAP;AACD;AACF,SAPW,EAOTJ,IAPS,CAOJ,YAAW;AACjBM,UAAAA,EAAE,CAAChC,KAAH,GADiB,CACL;;AACb,SATW,CAAd,CAFiC,CAajC;;AACA,YAAI,OAAOc,IAAI,CAACC,OAAZ,KAAwB,QAA5B,EAAsC;AACpCG,UAAAA,OAAO,CAACH,OAAR,CAAgBD,IAAI,CAACC,OAArB;AACD;AACF,OAjBD,MAiBO;AACL;AACAiB,QAAAA,EAAE,CAAChC,KAAH;AACD;AACF;AACF;AACF,CAnCD;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAzB,IAAI,CAAC4B,SAAL,CAAe4B,UAAf,GAA4B,YAAW;AACrC;AACA,MAAIrD,OAAO,GAAG,KAAKA,OAAnB;;AACA,OAAK,IAAI2D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG3D,OAAO,CAACkC,MAA5B,EAAoCyB,CAAC,EAArC,EAAyC;AACvC,QAAIP,MAAM,GAAGpD,OAAO,CAAC2D,CAAD,CAApB;;AACA,QAAIP,MAAM,CAACQ,IAAP,OAAkB,KAAtB,EAA6B;AAC3B,aAAOR,MAAP;AACD;AACF;;AAED,MAAIpD,OAAO,CAACkC,MAAR,GAAiB,KAAKpB,UAA1B,EAAsC;AACpC;AACAsC,IAAAA,MAAM,GAAG,KAAKS,oBAAL,EAAT;AACA7D,IAAAA,OAAO,CAACsC,IAAR,CAAac,MAAb;AACA,WAAOA,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CAlBD;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAvD,IAAI,CAAC4B,SAAL,CAAeiC,aAAf,GAA+B,UAASN,MAAT,EAAiB;AAC9C,MAAIE,EAAE,GAAG,IAAT;AAEA1D,EAAAA,oBAAoB,CAACkE,WAArB,CAAiCV,MAAM,CAACW,SAAxC,EAH8C,CAI9C;;AACA,OAAKC,qBAAL,CAA2BZ,MAA3B,EAL8C,CAM9C;;;AACA,OAAKhC,iBAAL,GAP8C,CAQ9C;;;AACA,SAAO,IAAI7B,OAAJ,CAAY,UAAS0E,OAAT,EAAkBC,MAAlB,EAA0B;AAC3Cd,IAAAA,MAAM,CAACe,SAAP,CAAiB,KAAjB,EAAwB,UAASC,GAAT,EAAc;AACpCd,MAAAA,EAAE,CAAC1C,iBAAH,CAAqB;AACnBV,QAAAA,QAAQ,EAAEkD,MAAM,CAAClD,QADE;AAEnBG,QAAAA,QAAQ,EAAE+C,MAAM,CAAC/C,QAFE;AAGnBP,QAAAA,MAAM,EAAEsD,MAAM,CAACtD;AAHI,OAArB;;AAKA,UAAIsE,GAAJ,EAAS;AACPF,QAAAA,MAAM,CAACE,GAAD,CAAN;AACD,OAFD,MAEO;AACLH,QAAAA,OAAO,CAACb,MAAD,CAAP;AACD;AACF,KAXD;AAYD,GAbM,CAAP;AAcD,CAvBD;AAyBA;AACA;AACA;AACA;AACA;;;AACAvD,IAAI,CAAC4B,SAAL,CAAeuC,qBAAf,GAAuC,UAASZ,MAAT,EAAiB;AACtD;AACA,MAAIiB,KAAK,GAAG,KAAKrE,OAAL,CAAa0C,OAAb,CAAqBU,MAArB,CAAZ;;AACA,MAAIiB,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChB,SAAKrE,OAAL,CAAasE,MAAb,CAAoBD,KAApB,EAA2B,CAA3B;AACD;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAxE,IAAI,CAAC4B,SAAL,CAAe0C,SAAf,GAA2B,UAAUI,KAAV,EAAiBlC,OAAjB,EAA0B;AACnD,MAAIiB,EAAE,GAAG,IAAT,CADmD,CAGnD;;AACA,OAAKrD,KAAL,CAAWiD,OAAX,CAAmB,UAAUd,IAAV,EAAgB;AACjCA,IAAAA,IAAI,CAACJ,QAAL,CAAckC,MAAd,CAAqB,IAAI/B,KAAJ,CAAU,iBAAV,CAArB;AACD,GAFD;AAGA,OAAKlC,KAAL,CAAWiC,MAAX,GAAoB,CAApB;;AAEA,MAAIsC,CAAC,GAAG,SAAJA,CAAI,CAAUpB,MAAV,EAAkB;AACxB,SAAKY,qBAAL,CAA2BZ,MAA3B;AACD,GAFD;;AAGA,MAAIqB,YAAY,GAAGD,CAAC,CAACjD,IAAF,CAAO,IAAP,CAAnB;AAEA,MAAImD,QAAQ,GAAG,EAAf;AACA,MAAI1E,OAAO,GAAG,KAAKA,OAAL,CAAamD,KAAb,EAAd;AACAnD,EAAAA,OAAO,CAACkD,OAAR,CAAgB,UAAUE,MAAV,EAAkB;AAChC,QAAIuB,WAAW,GAAGvB,MAAM,CAACwB,kBAAP,CAA0BL,KAA1B,EAAiClC,OAAjC,EACfW,IADe,CACVyB,YADU,EAEfI,MAFe,CAER,YAAW;AACjBvB,MAAAA,EAAE,CAAC1C,iBAAH,CAAqB;AACnBV,QAAAA,QAAQ,EAAEkD,MAAM,CAAClD,QADE;AAEnBG,QAAAA,QAAQ,EAAE+C,MAAM,CAAC/C,QAFE;AAGnBP,QAAAA,MAAM,EAAEsD,MAAM,CAACtD;AAHI,OAArB;AAKD,KARe,CAAlB;AASA4E,IAAAA,QAAQ,CAACpC,IAAT,CAAcqC,WAAd;AACD,GAXD;AAYA,SAAOpF,OAAO,CAACuF,GAAR,CAAYJ,QAAZ,CAAP;AACD,CA7BD;AA+BA;AACA;AACA;AACA;;;AACA7E,IAAI,CAAC4B,SAAL,CAAesD,KAAf,GAAuB,YAAY;AACjC,MAAIC,YAAY,GAAG,KAAKhF,OAAL,CAAakC,MAAhC;AACA,MAAI+C,WAAW,GAAG,KAAKjF,OAAL,CAAakF,MAAb,CAAoB,UAAU9B,MAAV,EAAkB;AACtD,WAAOA,MAAM,CAACQ,IAAP,EAAP;AACD,GAFiB,EAEf1B,MAFH;AAIA,SAAO;AACL8C,IAAAA,YAAY,EAAGA,YADV;AAELC,IAAAA,WAAW,EAAIA,WAFV;AAGLE,IAAAA,WAAW,EAAIH,YAAY,GAAGC,WAHzB;AAKLG,IAAAA,YAAY,EAAG,KAAKnF,KAAL,CAAWiC,MALrB;AAMLmD,IAAAA,WAAW,EAAIJ;AANV,GAAP;AAQD,CAdD;AAgBA;AACA;AACA;AACA;;;AACApF,IAAI,CAAC4B,SAAL,CAAeL,iBAAf,GAAmC,YAAW;AAC5C,MAAI,KAAKF,UAAT,EAAqB;AACnB,SAAI,IAAIyC,CAAC,GAAG,KAAK3D,OAAL,CAAakC,MAAzB,EAAiCyB,CAAC,GAAG,KAAKzC,UAA1C,EAAsDyC,CAAC,EAAvD,EAA2D;AACzD,WAAK3D,OAAL,CAAasC,IAAb,CAAkB,KAAKuB,oBAAL,EAAlB;AACD;AACF;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;;;AACAhE,IAAI,CAAC4B,SAAL,CAAeoC,oBAAf,GAAsC,YAAY;AAChD,MAAMyB,eAAe,GAAG,KAAK3E,cAAL,CAAoB;AAC1CT,IAAAA,QAAQ,EAAE,KAAKA,QAD2B;AAE1CG,IAAAA,QAAQ,EAAE,KAAKA,QAF2B;AAG1CP,IAAAA,MAAM,EAAE,KAAKA;AAH6B,GAApB,KAIlB,EAJN;AAMA,SAAO,IAAIL,aAAJ,CAAkB6F,eAAe,CAACxF,MAAhB,IAA0B,KAAKA,MAAjD,EAAyD;AAC9DI,IAAAA,QAAQ,EAAEoF,eAAe,CAACpF,QAAhB,IAA4B,KAAKA,QADmB;AAE9DG,IAAAA,QAAQ,EAAEiF,eAAe,CAACjF,QAAhB,IAA4B,KAAKA,QAFmB;AAG9D0D,IAAAA,SAAS,EAAEnE,oBAAoB,CAAC2F,uBAArB,CAA6C,KAAKjF,cAAlD,CAHmD;AAI9DE,IAAAA,UAAU,EAAE,KAAKA;AAJ6C,GAAzD,CAAP;AAMD,CAbD;AAeA;AACA;AACA;AACA;AACA;;;AACA,SAASK,kBAAT,CAA4BC,UAA5B,EAAwC;AACtC,MAAI,CAAC0E,QAAQ,CAAC1E,UAAD,CAAT,IAAyB,CAAC2E,SAAS,CAAC3E,UAAD,CAAnC,IAAmDA,UAAU,GAAG,CAApE,EAAuE;AACrE,UAAM,IAAIiB,SAAJ,CAAc,kDAAd,CAAN;AACD;AACF;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASZ,kBAAT,CAA4BD,UAA5B,EAAwC;AACtC,MAAI,CAACsE,QAAQ,CAACtE,UAAD,CAAT,IAAyB,CAACuE,SAAS,CAACvE,UAAD,CAAnC,IAAmDA,UAAU,GAAG,CAApE,EAAuE;AACrE,UAAM,IAAIa,SAAJ,CAAc,kDAAd,CAAN;AACD;AACF;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASyD,QAAT,CAAkBE,KAAlB,EAAyB;AACvB,SAAO,OAAOA,KAAP,KAAiB,QAAxB;AACD;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASD,SAAT,CAAmBC,KAAnB,EAA0B;AACxB,SAAO3E,IAAI,CAAC4E,KAAL,CAAWD,KAAX,KAAqBA,KAA5B;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBhG,IAAjB;;;;;;;;ACxba;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASN,OAAT,CAAiBuG,OAAjB,EAA0BC,MAA1B,EAAkC;AAChC,MAAIzC,EAAE,GAAG,IAAT;;AAEA,MAAI,EAAE,gBAAgB/D,OAAlB,CAAJ,EAAgC;AAC9B,UAAM,IAAIyG,WAAJ,CAAgB,kDAAhB,CAAN;AACD;;AAED,MAAI,OAAOF,OAAP,KAAmB,UAAvB,EAAmC;AACjC,UAAM,IAAIE,WAAJ,CAAgB,qDAAhB,CAAN;AACD;;AAED,MAAIC,UAAU,GAAG,EAAjB;AACA,MAAIC,OAAO,GAAG,EAAd,CAZgC,CAchC;;AACA,OAAKC,QAAL,GAAgB,KAAhB;AACA,OAAKC,QAAL,GAAgB,KAAhB;AACA,OAAK5C,OAAL,GAAe,IAAf;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;;AACE,MAAI6C,QAAQ,GAAG,kBAAUC,SAAV,EAAqBC,MAArB,EAA6B;AAC1CN,IAAAA,UAAU,CAAC3D,IAAX,CAAgBgE,SAAhB;;AACAJ,IAAAA,OAAO,CAAC5D,IAAR,CAAaiE,MAAb;AACD,GAHD;AAKA;AACF;AACA;AACA;AACA;AACA;;;AACE,OAAKvD,IAAL,GAAY,UAAUsD,SAAV,EAAqBC,MAArB,EAA6B;AACvC,WAAO,IAAIhH,OAAJ,CAAY,UAAU0E,OAAV,EAAmBC,MAAnB,EAA2B;AAC5C,UAAIsC,CAAC,GAAGF,SAAS,GAAGG,KAAK,CAACH,SAAD,EAAYrC,OAAZ,EAAqBC,MAArB,CAAR,GAAuCD,OAAxD;AACA,UAAIO,CAAC,GAAG+B,MAAM,GAAME,KAAK,CAACF,MAAD,EAAYtC,OAAZ,EAAqBC,MAArB,CAAX,GAA0CA,MAAxD;;AAEAmC,MAAAA,QAAQ,CAACG,CAAD,EAAIhC,CAAJ,CAAR;AACD,KALM,EAKJlB,EALI,CAAP;AAMD,GAPD;AASA;AACF;AACA;AACA;AACA;;;AACE,MAAIoD,SAAQ,GAAG,kBAAUC,MAAV,EAAkB;AAC/B;AACArD,IAAAA,EAAE,CAAC6C,QAAH,GAAc,IAAd;AACA7C,IAAAA,EAAE,CAAC8C,QAAH,GAAc,KAAd;AACA9C,IAAAA,EAAE,CAACE,OAAH,GAAa,KAAb;;AAEAyC,IAAAA,UAAU,CAAC/C,OAAX,CAAmB,UAAU0D,EAAV,EAAc;AAC/BA,MAAAA,EAAE,CAACD,MAAD,CAAF;AACD,KAFD;;AAIAN,IAAAA,QAAQ,GAAG,kBAAUC,SAAV,EAAqBC,MAArB,EAA6B;AACtCD,MAAAA,SAAS,CAACK,MAAD,CAAT;AACD,KAFD;;AAIAD,IAAAA,SAAQ,GAAGG,QAAO,GAAG,mBAAY,CAAG,CAApC;;AAEA,WAAOvD,EAAP;AACD,GAjBD;AAmBA;AACF;AACA;AACA;AACA;;;AACE,MAAIuD,QAAO,GAAG,iBAAUC,KAAV,EAAiB;AAC7B;AACAxD,IAAAA,EAAE,CAAC6C,QAAH,GAAc,KAAd;AACA7C,IAAAA,EAAE,CAAC8C,QAAH,GAAc,IAAd;AACA9C,IAAAA,EAAE,CAACE,OAAH,GAAa,KAAb;;AAEA0C,IAAAA,OAAO,CAAChD,OAAR,CAAgB,UAAU0D,EAAV,EAAc;AAC5BA,MAAAA,EAAE,CAACE,KAAD,CAAF;AACD,KAFD;;AAIAT,IAAAA,QAAQ,GAAG,kBAAUC,SAAV,EAAqBC,MAArB,EAA6B;AACtCA,MAAAA,MAAM,CAACO,KAAD,CAAN;AACD,KAFD;;AAIAJ,IAAAA,SAAQ,GAAGG,QAAO,GAAG,mBAAY,CAAG,CAApC;;AAEA,WAAOvD,EAAP;AACD,GAjBD;AAmBA;AACF;AACA;AACA;;;AACE,OAAKyD,MAAL,GAAc,YAAY;AACxB,QAAIhB,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAACgB,MAAP;AACD,KAFD,MAGK;AACHF,MAAAA,QAAO,CAAC,IAAIG,iBAAJ,EAAD,CAAP;AACD;;AAED,WAAO1D,EAAP;AACD,GATD;AAWA;AACF;AACA;AACA;AACA;AACA;AACA;;;AACE,OAAKjB,OAAL,GAAe,UAAUI,KAAV,EAAiB;AAC9B,QAAIsD,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAAC1D,OAAP,CAAeI,KAAf;AACD,KAFD,MAGK;AACH,UAAIwE,KAAK,GAAGC,UAAU,CAAC,YAAY;AACjCL,QAAAA,QAAO,CAAC,IAAIM,YAAJ,CAAiB,6BAA6B1E,KAA7B,GAAqC,KAAtD,CAAD,CAAP;AACD,OAFqB,EAEnBA,KAFmB,CAAtB;AAIAa,MAAAA,EAAE,CAACuB,MAAH,CAAU,YAAY;AACpBuC,QAAAA,YAAY,CAACH,KAAD,CAAZ;AACD,OAFD;AAGD;;AAED,WAAO3D,EAAP;AACD,GAfD,CApHgC,CAqIhC;;;AACAwC,EAAAA,OAAO,CAAC,UAAUa,MAAV,EAAkB;AACxBD,IAAAA,SAAQ,CAACC,MAAD,CAAR;AACD,GAFM,EAEJ,UAAUG,KAAV,EAAiB;AAClBD,IAAAA,QAAO,CAACC,KAAD,CAAP;AACD,GAJM,CAAP;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASL,KAAT,CAAeY,QAAf,EAAyBpD,OAAzB,EAAkCC,MAAlC,EAA0C;AACxC,SAAO,UAAUyC,MAAV,EAAkB;AACvB,QAAI;AACF,UAAIW,GAAG,GAAGD,QAAQ,CAACV,MAAD,CAAlB;;AACA,UAAIW,GAAG,IAAI,OAAOA,GAAG,CAACtE,IAAX,KAAoB,UAA3B,IAAyC,OAAOsE,GAAG,CAAC,OAAD,CAAV,KAAwB,UAArE,EAAiF;AAC/E;AACAA,QAAAA,GAAG,CAACtE,IAAJ,CAASiB,OAAT,EAAkBC,MAAlB;AACD,OAHD,MAIK;AACHD,QAAAA,OAAO,CAACqD,GAAD,CAAP;AACD;AACF,KATD,CAUA,OAAOR,KAAP,EAAc;AACZ5C,MAAAA,MAAM,CAAC4C,KAAD,CAAN;AACD;AACF,GAdD;AAeD;AAED;AACA;AACA;AACA;AACA;;;AACAvH,OAAO,CAACkC,SAAR,CAAkB,OAAlB,IAA6B,UAAU8E,MAAV,EAAkB;AAC7C,SAAO,KAAKvD,IAAL,CAAU,IAAV,EAAgBuD,MAAhB,CAAP;AACD,CAFD,EAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AACAhH,OAAO,CAACkC,SAAR,CAAkBoD,MAAlB,GAA2B,UAAU+B,EAAV,EAAc;AACvC,SAAO,KAAK5D,IAAL,CAAU4D,EAAV,EAAcA,EAAd,CAAP;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACArH,OAAO,CAACuF,GAAR,GAAc,UAAUJ,QAAV,EAAmB;AAC/B,SAAO,IAAInF,OAAJ,CAAY,UAAU0E,OAAV,EAAmBC,MAAnB,EAA2B;AAC5C,QAAIqD,SAAS,GAAG7C,QAAQ,CAACxC,MAAzB;AAAA,QACIsF,OAAO,GAAG,EADd;;AAGA,QAAID,SAAJ,EAAe;AACb7C,MAAAA,QAAQ,CAACxB,OAAT,CAAiB,UAAUuE,CAAV,EAAa9D,CAAb,EAAgB;AAC/B8D,QAAAA,CAAC,CAACzE,IAAF,CAAO,UAAU2D,MAAV,EAAkB;AACvBa,UAAAA,OAAO,CAAC7D,CAAD,CAAP,GAAagD,MAAb;AACAY,UAAAA,SAAS;;AACT,cAAIA,SAAS,IAAI,CAAjB,EAAoB;AAClBtD,YAAAA,OAAO,CAACuD,OAAD,CAAP;AACD;AACF,SAND,EAMG,UAAUV,KAAV,EAAiB;AAClBS,UAAAA,SAAS,GAAG,CAAZ;AACArD,UAAAA,MAAM,CAAC4C,KAAD,CAAN;AACD,SATD;AAUD,OAXD;AAYD,KAbD,MAcK;AACH7C,MAAAA,OAAO,CAACuD,OAAD,CAAP;AACD;AACF,GArBM,CAAP;AAsBD,CAvBD;AAyBA;AACA;AACA;AACA;;;AACAjI,OAAO,CAAC0C,KAAR,GAAgB,YAAY;AAC1B,MAAID,QAAQ,GAAG,EAAf;AAEAA,EAAAA,QAAQ,CAACQ,OAAT,GAAmB,IAAIjD,OAAJ,CAAY,UAAU0E,OAAV,EAAmBC,MAAnB,EAA2B;AACxDlC,IAAAA,QAAQ,CAACiC,OAAT,GAAmBA,OAAnB;AACAjC,IAAAA,QAAQ,CAACkC,MAAT,GAAkBA,MAAlB;AACD,GAHkB,CAAnB;AAKA,SAAOlC,QAAP;AACD,CATD;AAWA;AACA;AACA;AACA;AACA;;;AACA,SAASgF,iBAAT,CAA2BU,OAA3B,EAAoC;AAClC,OAAKA,OAAL,GAAeA,OAAO,IAAI,mBAA1B;AACA,OAAKC,KAAL,GAAc,IAAIxF,KAAJ,EAAD,CAAcwF,KAA3B;AACD;;AAEDX,iBAAiB,CAACvF,SAAlB,GAA8B,IAAIU,KAAJ,EAA9B;AACA6E,iBAAiB,CAACvF,SAAlB,CAA4BmG,WAA5B,GAA0CzF,KAA1C;AACA6E,iBAAiB,CAACvF,SAAlB,CAA4BoG,IAA5B,GAAmC,mBAAnC;AAEAtI,OAAO,CAACyH,iBAAR,GAA4BA,iBAA5B;AAGA;AACA;AACA;AACA;AACA;;AACA,SAASG,YAAT,CAAsBO,OAAtB,EAA+B;AAC7B,OAAKA,OAAL,GAAeA,OAAO,IAAI,kBAA1B;AACA,OAAKC,KAAL,GAAc,IAAIxF,KAAJ,EAAD,CAAcwF,KAA3B;AACD;;AAEDR,YAAY,CAAC1F,SAAb,GAAyB,IAAIU,KAAJ,EAAzB;AACAgF,YAAY,CAAC1F,SAAb,CAAuBmG,WAAvB,GAAqCzF,KAArC;AACAgF,YAAY,CAAC1F,SAAb,CAAuBoG,IAAvB,GAA8B,cAA9B;AAEAtI,OAAO,CAAC4H,YAAR,GAAuBA,YAAvB;AAGAvB,MAAM,CAACC,OAAP,GAAiBtG,OAAjB;;;;;;;;ACtRa;;;;;;;;;;AAEb,IAAIA,OAAO,GAAGC,mBAAO,CAAC,GAAD,CAArB;;AACA,IAAIE,WAAW,GAAGF,mBAAO,CAAC,GAAD,CAAzB;;AACA,IAAIsI,kBAAkB,GAAGtI,mBAAO,CAAC,GAAD,CAAhC;AAEA;AACA;AACA;AACA;;;AACA,IAAIuI,mBAAmB,GAAG,0BAA1B;AAEA;AACA;AACA;AACA;;AACA,IAAIC,0BAA0B,GAAG,IAAjC;;AAEA,SAASxG,mBAAT,GAA+B;AAC7B,MAAIyG,aAAa,GAAGC,uBAAuB,EAA3C;;AACA,MAAI,CAACD,aAAL,EAAoB;AAClB,UAAM,IAAI9F,KAAJ,CAAU,+EAAV,CAAN;AACD;;AAED,SAAO8F,aAAP;AACD,EAED;;;AACA,SAASE,eAAT,GAA2B;AACzB;AACA,MAAI,OAAOC,MAAP,KAAkB,UAAlB,KAAiC,QAAOA,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8B,OAAOA,MAAM,CAAC3G,SAAP,CAAiBmG,WAAxB,KAAwC,UAAvG,CAAJ,EAAwH;AACtH,UAAM,IAAIzF,KAAJ,CAAU,uCAAV,CAAN;AACD;AACF;;AAED,SAAS+F,uBAAT,GAAmC;AACjC,MAAI;AACF,WAAOJ,kBAAkB,CAAC,gBAAD,CAAzB;AACD,GAFD,CAEE,OAAMhB,KAAN,EAAa;AACb,QAAI,QAAOA,KAAP,MAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAAvC,IAA+CA,KAAK,CAACuB,IAAN,KAAe,kBAAlE,EAAsF;AACpF;AACA,aAAO,IAAP;AACD,KAHD,MAGO;AACL,YAAMvB,KAAN;AACD;AACF;AACF,EAED;;;AACA,SAASwB,gBAAT,GAA4B;AAC1B,MAAI5I,WAAW,CAAC6I,QAAZ,KAAyB,SAA7B,EAAwC;AACtC;AACA,QAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;AAC/B,YAAM,IAAIrG,KAAJ,CAAU,mCAAV,CAAN;AACD;;AACD,QAAI,CAACsG,MAAM,CAACC,GAAR,IAAe,OAAOD,MAAM,CAACC,GAAP,CAAWC,eAAlB,KAAsC,UAAzD,EAAqE;AACnE,YAAM,IAAIxG,KAAJ,CAAU,kDAAV,CAAN;AACD,KAPqC,CAStC;;;AACA,QAAIyG,IAAI,GAAG,IAAIJ,IAAJ,CAAS,CAAChJ,mBAAO,CAAC,GAAD,CAAR,CAAT,EAAkD;AAACqJ,MAAAA,IAAI,EAAE;AAAP,KAAlD,CAAX;AACA,WAAOJ,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BC,IAA3B,CAAP;AACD,GAZD,MAaK;AACH;AACA,WAAOE,SAAS,GAAG,YAAnB;AACD;AACF;;AAED,SAASC,WAAT,CAAqBjJ,MAArB,EAA6BC,OAA7B,EAAsC;AACpC,MAAIA,OAAO,CAACS,UAAR,KAAuB,KAA3B,EAAkC;AAAE;AAClC2H,IAAAA,eAAe;AACf,WAAOa,kBAAkB,CAAClJ,MAAD,EAASsI,MAAT,CAAzB;AACD,GAHD,MAGO,IAAIrI,OAAO,CAACS,UAAR,KAAuB,QAA3B,EAAqC;AAAE;AAC5CyH,IAAAA,aAAa,GAAGzG,mBAAmB,EAAnC;AACA,WAAOyH,uBAAuB,CAACnJ,MAAD,EAASmI,aAAT,CAA9B;AACD,GAHM,MAGA,IAAIlI,OAAO,CAACS,UAAR,KAAuB,SAAvB,IAAoC,CAACT,OAAO,CAACS,UAAjD,EAA6D;AAAE;AACpE,WAAO0I,kBAAkB,CAACpJ,MAAD,EAASqJ,kBAAkB,CAACpJ,OAAD,CAA3B,EAAsC+H,kBAAkB,CAAC,eAAD,CAAxD,CAAzB;AACD,GAFM,MAEA;AAAE;AACP,QAAIpI,WAAW,CAAC6I,QAAZ,KAAyB,SAA7B,EAAwC;AACtCJ,MAAAA,eAAe;AACf,aAAOa,kBAAkB,CAAClJ,MAAD,EAASsI,MAAT,CAAzB;AACD,KAHD,MAIK;AAAE;AACL,UAAIH,aAAa,GAAGC,uBAAuB,EAA3C;;AACA,UAAID,aAAJ,EAAmB;AACjB,eAAOgB,uBAAuB,CAACnJ,MAAD,EAASmI,aAAT,CAA9B;AACD,OAFD,MAEO;AACL,eAAOiB,kBAAkB,CAACpJ,MAAD,EAASqJ,kBAAkB,CAACpJ,OAAD,CAA3B,EAAsC+H,kBAAkB,CAAC,eAAD,CAAxD,CAAzB;AACD;AACF;AACF;AACF;;AAED,SAASkB,kBAAT,CAA4BlJ,MAA5B,EAAoCsI,MAApC,EAA4C;AAC1C;AACA,MAAIhF,MAAM,GAAG,IAAIgF,MAAJ,CAAWtI,MAAX,CAAb;AAEAsD,EAAAA,MAAM,CAACgG,eAAP,GAAyB,IAAzB,CAJ0C,CAK1C;;AACAhG,EAAAA,MAAM,CAACiG,EAAP,GAAY,UAAUC,KAAV,EAAiBjC,QAAjB,EAA2B;AACrC,SAAKkC,gBAAL,CAAsBD,KAAtB,EAA6B,UAAU5B,OAAV,EAAmB;AAC9CL,MAAAA,QAAQ,CAACK,OAAO,CAAC8B,IAAT,CAAR;AACD,KAFD;AAGD,GAJD;;AAKApG,EAAAA,MAAM,CAACqG,IAAP,GAAc,UAAU/B,OAAV,EAAmB;AAC/B,SAAKgC,WAAL,CAAiBhC,OAAjB;AACD,GAFD;;AAGA,SAAOtE,MAAP;AACD;;AAED,SAAS6F,uBAAT,CAAiCnJ,MAAjC,EAAyCmI,aAAzC,EAAwD;AACtD,MAAI7E,MAAM,GAAG,IAAI6E,aAAa,CAACG,MAAlB,CAAyBtI,MAAzB,EAAiC;AAC5C6J,IAAAA,MAAM,EAAE,KADoC;AAC7B;AACfC,IAAAA,MAAM,EAAE,KAFoC,CAE7B;;AAF6B,GAAjC,CAAb;AAIAxG,EAAAA,MAAM,CAACyG,cAAP,GAAwB,IAAxB,CALsD,CAMtD;;AACAzG,EAAAA,MAAM,CAACqG,IAAP,GAAc,UAAS/B,OAAT,EAAkB;AAC9B,SAAKgC,WAAL,CAAiBhC,OAAjB;AACD,GAFD;;AAIAtE,EAAAA,MAAM,CAAC0G,IAAP,GAAc,YAAW;AACvB,SAAK3F,SAAL;AACA,WAAO,IAAP;AACD,GAHD;;AAKAf,EAAAA,MAAM,CAAC2G,UAAP,GAAoB,YAAW;AAC7B,SAAK5F,SAAL;AACD,GAFD;;AAIA,SAAOf,MAAP;AACD;;AAED,SAAS8F,kBAAT,CAA4BpJ,MAA5B,EAAoCC,OAApC,EAA6CiK,aAA7C,EAA4D;AAC1D;AACA,MAAI5G,MAAM,GAAG4G,aAAa,CAACC,IAAd,CACXnK,MADW,EAEXC,OAAO,CAACG,QAFG,EAGXH,OAAO,CAACM,QAHG,CAAb;AAMA+C,EAAAA,MAAM,CAAC8G,cAAP,GAAwB,IAAxB;AACA,SAAO9G,MAAP;AACD,EAED;;;AACA,SAAS+F,kBAAT,CAA4BgB,IAA5B,EAAkC;AAChCA,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;AAEA,MAAIC,eAAe,GAAGC,OAAO,CAACC,QAAR,CAAiBC,IAAjB,CAAsB,GAAtB,CAAtB;AACA,MAAIC,eAAe,GAAGJ,eAAe,CAAC1H,OAAhB,CAAwB,WAAxB,MAAyC,CAAC,CAAhE;AACA,MAAI+H,QAAQ,GAAGL,eAAe,CAAC1H,OAAhB,CAAwB,aAAxB,MAA2C,CAAC,CAA3D;AAEA,MAAI4H,QAAQ,GAAG,EAAf;;AACA,MAAIE,eAAJ,EAAqB;AACnBF,IAAAA,QAAQ,CAAChI,IAAT,CAAc,eAAe6H,IAAI,CAACpG,SAAlC;;AAEA,QAAI0G,QAAJ,EAAc;AACZH,MAAAA,QAAQ,CAAChI,IAAT,CAAc,aAAd;AACD;AACF;;AAED+H,EAAAA,OAAO,CAACC,QAAR,CAAiBpH,OAAjB,CAAyB,UAASwH,GAAT,EAAc;AACrC,QAAIA,GAAG,CAAChI,OAAJ,CAAY,sBAAZ,IAAsC,CAAC,CAA3C,EAA8C;AAC5C4H,MAAAA,QAAQ,CAAChI,IAAT,CAAcoI,GAAd;AACD;AACF,GAJD;AAMA,SAAOvK,MAAM,CAACwK,MAAP,CAAc,EAAd,EAAkBR,IAAlB,EAAwB;AAC7BjK,IAAAA,QAAQ,EAAEiK,IAAI,CAACjK,QADc;AAE7BG,IAAAA,QAAQ,EAAEF,MAAM,CAACwK,MAAP,CAAc,EAAd,EAAkBR,IAAI,CAAC9J,QAAvB,EAAiC;AACzCiK,MAAAA,QAAQ,EAAE,CAACH,IAAI,CAAC9J,QAAL,IAAiB8J,IAAI,CAAC9J,QAAL,CAAciK,QAA/B,IAA2C,EAA5C,EACTM,MADS,CACFN,QADE;AAD+B,KAAjC;AAFmB,GAAxB,CAAP;AAOD;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASO,aAAT,CAAwBC,GAAxB,EAA6B;AAC3B,MAAIC,IAAI,GAAG,IAAI5I,KAAJ,CAAU,EAAV,CAAX;AACA,MAAI6I,KAAK,GAAG7K,MAAM,CAAC8K,IAAP,CAAYH,GAAZ,CAAZ;;AAEA,OAAK,IAAInH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqH,KAAK,CAAC9I,MAA1B,EAAkCyB,CAAC,EAAnC,EAAuC;AACrCoH,IAAAA,IAAI,CAACC,KAAK,CAACrH,CAAD,CAAN,CAAJ,GAAiBmH,GAAG,CAACE,KAAK,CAACrH,CAAD,CAAN,CAApB;AACD;;AAED,SAAOoH,IAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAStL,aAAT,CAAuBK,MAAvB,EAA+BoL,QAA/B,EAAyC;AACvC,MAAI5H,EAAE,GAAG,IAAT;AACA,MAAIvD,OAAO,GAAGmL,QAAQ,IAAI,EAA1B;AAEA,OAAKpL,MAAL,GAAcA,MAAM,IAAIwI,gBAAgB,EAAxC;AACA,OAAKlF,MAAL,GAAc2F,WAAW,CAAC,KAAKjJ,MAAN,EAAcC,OAAd,CAAzB;AACA,OAAKgE,SAAL,GAAiBhE,OAAO,CAACgE,SAAzB;AACA,OAAK1D,QAAL,GAAgBN,OAAO,CAACM,QAAxB;AACA,OAAKH,QAAL,GAAgBH,OAAO,CAACG,QAAxB,CARuC,CAUvC;;AACA,MAAI,CAACJ,MAAL,EAAa;AACX,SAAKsD,MAAL,CAAY+H,KAAZ,GAAoB,IAApB;AACD,GAbsC,CAevC;;;AACA,OAAKC,YAAL,GAAoB,EAApB;AACA,OAAKhI,MAAL,CAAYiG,EAAZ,CAAe,SAAf,EAA0B,UAAUgC,QAAV,EAAoB;AAC5C,QAAI/H,EAAE,CAACG,UAAP,EAAmB;AACjB;AACD;;AACD,QAAI,OAAO4H,QAAP,KAAoB,QAApB,IAAgCA,QAAQ,KAAK,OAAjD,EAA0D;AACxD/H,MAAAA,EAAE,CAACF,MAAH,CAAU+H,KAAV,GAAkB,IAAlB;AACAG,MAAAA,sBAAsB;AACvB,KAHD,MAGO;AACL;AACA,UAAIC,EAAE,GAAGF,QAAQ,CAACE,EAAlB;AACA,UAAInJ,IAAI,GAAGkB,EAAE,CAACkI,UAAH,CAAcD,EAAd,CAAX;;AACA,UAAInJ,IAAI,KAAKqJ,SAAb,EAAwB;AACtB,YAAIJ,QAAQ,CAACK,OAAb,EAAsB;AACpB,cAAItJ,IAAI,CAACrC,OAAL,IAAgB,OAAOqC,IAAI,CAACrC,OAAL,CAAasJ,EAApB,KAA2B,UAA/C,EAA2D;AACzDjH,YAAAA,IAAI,CAACrC,OAAL,CAAasJ,EAAb,CAAgBgC,QAAQ,CAACM,OAAzB;AACD;AACF,SAJD,MAIO;AACL;AACA,iBAAOrI,EAAE,CAACkI,UAAH,CAAcD,EAAd,CAAP,CAFK,CAIL;;AACA,cAAIjI,EAAE,CAACsI,WAAH,KAAmB,IAAvB,EAA6B;AAC3B;AACAtI,YAAAA,EAAE,CAACa,SAAH;AACD,WARI,CAUL;;;AACA,cAAIkH,QAAQ,CAACvE,KAAb,EAAoB;AAClB1E,YAAAA,IAAI,CAACJ,QAAL,CAAckC,MAAd,CAAqB2G,aAAa,CAACQ,QAAQ,CAACvE,KAAV,CAAlC;AACD,WAFD,MAGK;AACH1E,YAAAA,IAAI,CAACJ,QAAL,CAAciC,OAAd,CAAsBoH,QAAQ,CAAC1E,MAA/B;AACD;AACF;AACF;AACF;AACF,GApCD,EAjBuC,CAuDvC;;AACA,WAASkF,OAAT,CAAiB/E,KAAjB,EAAwB;AACtBxD,IAAAA,EAAE,CAACG,UAAH,GAAgB,IAAhB;;AAEA,SAAK,IAAI8H,EAAT,IAAejI,EAAE,CAACkI,UAAlB,EAA8B;AAC5B,UAAIlI,EAAE,CAACkI,UAAH,CAAcD,EAAd,MAAsBE,SAA1B,EAAqC;AACnCnI,QAAAA,EAAE,CAACkI,UAAH,CAAcD,EAAd,EAAkBvJ,QAAlB,CAA2BkC,MAA3B,CAAkC4C,KAAlC;AACD;AACF;;AACDxD,IAAAA,EAAE,CAACkI,UAAH,GAAgBrL,MAAM,CAAC2L,MAAP,CAAc,IAAd,CAAhB;AACD,GAjEsC,CAmEvC;;;AACA,WAASR,sBAAT,GACA;AAAA,+CACuBhI,EAAE,CAAC8H,YAAH,CAAgB9G,MAAhB,CAAuB,CAAvB,CADvB;AAAA;;AAAA;AACE,0DAAgD;AAAA,YAAtCyH,OAAsC;AAC9CzI,QAAAA,EAAE,CAACF,MAAH,CAAUqG,IAAV,CAAesC,OAAf;AACD;AAHH;AAAA;AAAA;AAAA;AAAA;AAIC;;AAED,MAAI3I,MAAM,GAAG,KAAKA,MAAlB,CA3EuC,CA4EvC;;AACA,OAAKA,MAAL,CAAYiG,EAAZ,CAAe,OAAf,EAAwBwC,OAAxB;AACA,OAAKzI,MAAL,CAAYiG,EAAZ,CAAe,MAAf,EAAuB,UAAU2C,QAAV,EAAoBC,UAApB,EAAgC;AACrD,QAAIvE,OAAO,GAAG,6CAAd;AAEAA,IAAAA,OAAO,IAAI,oBAAoBsE,QAApB,GAA+B,KAA1C;AACAtE,IAAAA,OAAO,IAAI,sBAAsBuE,UAAtB,GAAmC,KAA9C;AAEAvE,IAAAA,OAAO,IAAI,6BAA8BpE,EAAE,CAACxD,MAAjC,GAA0C,KAArD;AACA4H,IAAAA,OAAO,IAAI,qBAAsBtE,MAAM,CAAC8I,SAA7B,GAAyC,KAApD;AACAxE,IAAAA,OAAO,IAAI,qBAAqBtE,MAAM,CAAC+I,SAA5B,GAAwC,KAAnD;AAEAzE,IAAAA,OAAO,IAAI,kBAAkBtE,MAAM,CAACuG,MAAzB,GAAkC,KAA7C;AACAjC,IAAAA,OAAO,IAAI,kBAAkBtE,MAAM,CAACwG,MAAzB,GAAkC,KAA7C;AAEAiC,IAAAA,OAAO,CAAC,IAAI1J,KAAJ,CAAUuF,OAAV,CAAD,CAAP;AACD,GAdD;AAgBA,OAAK8D,UAAL,GAAkBrL,MAAM,CAAC2L,MAAP,CAAc,IAAd,CAAlB,CA9FuC,CA8FA;;AAEvC,OAAKF,WAAL,GAAmB,KAAnB;AACA,OAAKnI,UAAL,GAAkB,KAAlB;AACA,OAAK2I,kBAAL,GAA0B,IAA1B;AACA,OAAKC,MAAL,GAAc,CAAd;AACD;AAED;AACA;AACA;AACA;;;AACA5M,aAAa,CAACgC,SAAd,CAAwBwB,OAAxB,GAAkC,YAAY;AAC5C,SAAO,KAAKvB,IAAL,CAAU,SAAV,CAAP;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAjC,aAAa,CAACgC,SAAd,CAAwBC,IAAxB,GAA+B,UAASC,MAAT,EAAiBC,MAAjB,EAAyBI,QAAzB,EAAmCjC,OAAnC,EAA4C;AACzE,MAAI,CAACiC,QAAL,EAAe;AACbA,IAAAA,QAAQ,GAAGzC,OAAO,CAAC0C,KAAR,EAAX;AACD,GAHwE,CAKzE;;;AACA,MAAIsJ,EAAE,GAAG,EAAE,KAAKc,MAAhB,CANyE,CAQzE;;AACA,OAAKb,UAAL,CAAgBD,EAAhB,IAAsB;AACpBA,IAAAA,EAAE,EAAEA,EADgB;AAEpBvJ,IAAAA,QAAQ,EAAEA,QAFU;AAGpBjC,IAAAA,OAAO,EAAEA;AAHW,GAAtB,CATyE,CAezE;;AACA,MAAIgM,OAAO,GAAG;AACZR,IAAAA,EAAE,EAAEA,EADQ;AAEZ5J,IAAAA,MAAM,EAAEA,MAFI;AAGZC,IAAAA,MAAM,EAAEA;AAHI,GAAd;;AAMA,MAAI,KAAK6B,UAAT,EAAqB;AACnBzB,IAAAA,QAAQ,CAACkC,MAAT,CAAgB,IAAI/B,KAAJ,CAAU,sBAAV,CAAhB;AACD,GAFD,MAEO,IAAI,KAAKiB,MAAL,CAAY+H,KAAhB,EAAuB;AAC5B;AACA,SAAK/H,MAAL,CAAYqG,IAAZ,CAAiBsC,OAAjB;AACD,GAHM,MAGA;AACL,SAAKX,YAAL,CAAkB9I,IAAlB,CAAuByJ,OAAvB;AACD,GA7BwE,CA+BzE;;;AACA,MAAIzI,EAAE,GAAG,IAAT;AACA,SAAOtB,QAAQ,CAACQ,OAAT,UAAuB,UAAUsE,KAAV,EAAiB;AAC7C,QAAIA,KAAK,YAAYvH,OAAO,CAACyH,iBAAzB,IAA8CF,KAAK,YAAYvH,OAAO,CAAC4H,YAA3E,EAAyF;AACvF;AACA;AACA,aAAO7D,EAAE,CAACkI,UAAH,CAAcD,EAAd,CAAP,CAHuF,CAKvF;;AACA,aAAOjI,EAAE,CAACsB,kBAAH,CAAsB,IAAtB,EACJ5B,IADI,CACC,YAAW;AACf,cAAM8D,KAAN;AACD,OAHI,EAGF,UAAS1C,GAAT,EAAc;AACf,cAAMA,GAAN;AACD,OALI,CAAP;AAMD,KAZD,MAYO;AACL,YAAM0C,KAAN;AACD;AACF,GAhBM,CAAP;AAiBD,CAlDD;AAoDA;AACA;AACA;AACA;;;AACArH,aAAa,CAACgC,SAAd,CAAwBmC,IAAxB,GAA+B,YAAY;AACzC,SAAOzD,MAAM,CAAC8K,IAAP,CAAY,KAAKO,UAAjB,EAA6BtJ,MAA7B,GAAsC,CAA7C;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAzC,aAAa,CAACgC,SAAd,CAAwB0C,SAAxB,GAAoC,UAAUI,KAAV,EAAiB8C,QAAjB,EAA2B;AAC7D,MAAI/D,EAAE,GAAG,IAAT;;AACA,MAAIiB,KAAJ,EAAW;AACT;AACA,SAAK,IAAIgH,EAAT,IAAe,KAAKC,UAApB,EAAgC;AAC9B,UAAI,KAAKA,UAAL,CAAgBD,EAAhB,MAAwBE,SAA5B,EAAuC;AACrC,aAAKD,UAAL,CAAgBD,EAAhB,EAAoBvJ,QAApB,CAA6BkC,MAA7B,CAAoC,IAAI/B,KAAJ,CAAU,mBAAV,CAApC;AACD;AACF;;AACD,SAAKqJ,UAAL,GAAkBrL,MAAM,CAAC2L,MAAP,CAAc,IAAd,CAAlB;AACD;;AAED,MAAI,OAAOzE,QAAP,KAAoB,UAAxB,EAAoC;AAClC,SAAK+E,kBAAL,GAA0B/E,QAA1B;AACD;;AACD,MAAI,CAAC,KAAKzD,IAAL,EAAL,EAAkB;AAChB;AACA,QAAI0I,OAAO,GAAG,SAAVA,OAAU,CAASlI,GAAT,EAAc;AAC1Bd,MAAAA,EAAE,CAACG,UAAH,GAAgB,IAAhB;;AACA,UAAIH,EAAE,CAACF,MAAH,IAAa,IAAb,IAAqBE,EAAE,CAACF,MAAH,CAAUmJ,kBAAnC,EAAuD;AACrD;AACAjJ,QAAAA,EAAE,CAACF,MAAH,CAAUmJ,kBAAV,CAA6B,SAA7B;AACD;;AACDjJ,MAAAA,EAAE,CAACF,MAAH,GAAY,IAAZ;AACAE,MAAAA,EAAE,CAACsI,WAAH,GAAiB,KAAjB;;AACA,UAAItI,EAAE,CAAC8I,kBAAP,EAA2B;AACzB9I,QAAAA,EAAE,CAAC8I,kBAAH,CAAsBhI,GAAtB,EAA2Bd,EAA3B;AACD,OAFD,MAEO,IAAIc,GAAJ,EAAS;AACd,cAAMA,GAAN;AACD;AACF,KAbD;;AAeA,QAAI,KAAKhB,MAAT,EAAiB;AACf,UAAI,OAAO,KAAKA,MAAL,CAAY0G,IAAnB,KAA4B,UAAhC,EAA4C;AAC1C,YAAI,KAAK1G,MAAL,CAAYoJ,MAAhB,EAAwB;AACtBF,UAAAA,OAAO,CAAC,IAAInK,KAAJ,CAAU,wBAAV,CAAD,CAAP;AACA;AACD;;AAED,YAAI,KAAKiB,MAAL,CAAY8G,cAAhB,EAAgC;AAC9B,cAAIuC,gBAAgB,GAAGvF,UAAU,CAAC,YAAW;AAC3C,gBAAI5D,EAAE,CAACF,MAAP,EAAe;AACbE,cAAAA,EAAE,CAACF,MAAH,CAAU0G,IAAV;AACD;AACF,WAJgC,EAI9B9B,0BAJ8B,CAAjC;AAMA,eAAK5E,MAAL,CAAYsJ,IAAZ,CAAiB,MAAjB,EAAyB,YAAW;AAClCtF,YAAAA,YAAY,CAACqF,gBAAD,CAAZ;;AACA,gBAAInJ,EAAE,CAACF,MAAP,EAAe;AACbE,cAAAA,EAAE,CAACF,MAAH,CAAUoJ,MAAV,GAAmB,IAAnB;AACD;;AACDF,YAAAA,OAAO;AACR,WAND;;AAQA,cAAI,KAAKlJ,MAAL,CAAY+H,KAAhB,EAAuB;AACrB,iBAAK/H,MAAL,CAAYqG,IAAZ,CAAiB1B,mBAAjB;AACD,WAFD,MAEO;AACL,iBAAK3E,MAAL,CAAYgI,YAAZ,CAAyB9I,IAAzB,CAA8ByF,mBAA9B;AACD;AACF,SApBD,MAoBO;AACL;AACA,eAAK3E,MAAL,CAAY0G,IAAZ;AACA,eAAK1G,MAAL,CAAYoJ,MAAZ,GAAqB,IAArB;AACAF,UAAAA,OAAO;AACR;;AACD;AACD,OAjCD,MAkCK,IAAI,OAAO,KAAKlJ,MAAL,CAAYe,SAAnB,KAAiC,UAArC,EAAiD;AACpD,aAAKf,MAAL,CAAYe,SAAZ,GADoD,CAC3B;;AACzB,aAAKf,MAAL,CAAYoJ,MAAZ,GAAqB,IAArB;AACD,OAHI,MAIA;AACH,cAAM,IAAIrK,KAAJ,CAAU,4BAAV,CAAN;AACD;AACF;;AACDmK,IAAAA,OAAO;AACR,GA7DD,MA8DK;AACH;AACA,SAAKV,WAAL,GAAmB,IAAnB;AACD;AACF,CAjFD;AAmFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAnM,aAAa,CAACgC,SAAd,CAAwBmD,kBAAxB,GAA6C,UAAUL,KAAV,EAAiBlC,OAAjB,EAA0B;AACrE,MAAIL,QAAQ,GAAGzC,OAAO,CAAC0C,KAAR,EAAf;;AACA,MAAII,OAAJ,EAAa;AACXL,IAAAA,QAAQ,CAACQ,OAAT,CAAiBH,OAAjB,GAA2BA,OAA3B;AACD;;AACD,OAAK8B,SAAL,CAAeI,KAAf,EAAsB,UAASH,GAAT,EAAchB,MAAd,EAAsB;AAC1C,QAAIgB,GAAJ,EAAS;AACPpC,MAAAA,QAAQ,CAACkC,MAAT,CAAgBE,GAAhB;AACD,KAFD,MAEO;AACLpC,MAAAA,QAAQ,CAACiC,OAAT,CAAiBb,MAAjB;AACD;AACF,GAND;AAOA,SAAOpB,QAAQ,CAACQ,OAAhB;AACD,CAbD;;AAeAoD,MAAM,CAACC,OAAP,GAAiBpG,aAAjB;AACAmG,uCAAA,GAA0CsC,uBAA1C;AACAtC,kCAAA,GAAqCsD,kBAArC;AACAtD,kCAAA,GAAqCoD,kBAArC;AACApD,uCAAA,GAA0CqD,uBAA1C;AACArD,kCAAA,GAAqCpE,mBAArC;;;;;;;;ACrfa;;AAEb,IAAIuL,SAAS,GAAG,KAAhB;AACAnH,MAAM,CAACC,OAAP,GAAiBlG,kBAAjB;;AACA,SAASA,kBAAT,GAA8B;AAC5B,OAAKqN,KAAL,GAAa7M,MAAM,CAAC2L,MAAP,CAAc,IAAd,CAAb;AACA,OAAK5J,MAAL,GAAc,CAAd;AACD;;AAEDvC,kBAAkB,CAAC8B,SAAnB,CAA6B8D,uBAA7B,GAAuD,UAAS0H,QAAT,EAAmB;AACxE,SAAO,KAAKD,KAAL,CAAWC,QAAX,MAAyB,IAAhC,EAAsC;AACpCA,IAAAA,QAAQ;AACT;;AAED,MAAIA,QAAQ,IAAIF,SAAhB,EAA2B;AACzB,UAAM,IAAI5K,KAAJ,CAAU,0CAA0C8K,QAA1C,GAAqD,KAArD,GAA6DF,SAAvE,CAAN;AACD;;AAED,OAAKC,KAAL,CAAWC,QAAX,IAAuB,IAAvB;AACA,OAAK/K,MAAL;AACA,SAAO+K,QAAP;AACD,CAZD;;AAcAtN,kBAAkB,CAAC8B,SAAnB,CAA6BqC,WAA7B,GAA2C,UAASoJ,IAAT,EAAe;AACxD,SAAO,KAAKF,KAAL,CAAWE,IAAX,CAAP;AACA,OAAKhL,MAAL;AACD,CAHD;;;;;;;ACvBA,IAAI4F,kBAAkB,GAAGtI,mBAAO,CAAC,GAAD,CAAhC,EAEA;;;AACA,IAAI2N,MAAM,GAAG,SAATA,MAAS,CAAUC,WAAV,EAAuB;AAClC,SACE,OAAOA,WAAP,KAAuB,WAAvB,IACAA,WAAW,CAACC,QAAZ,IAAwB,IADxB,IAEAD,WAAW,CAACC,QAAZ,CAAqBC,IAArB,IAA6B,IAH/B;AAKD,CAND;;AAOA1H,qBAAA,GAAwBuH,MAAxB,EAEA;;AACAvH,uBAAA,GAA0B,OAAOyE,OAAP,KAAmB,WAAnB,IAAkC8C,MAAM,CAAC9C,OAAD,CAAxC,GACtB,MADsB,GAEtB,SAFJ,EAIA;AACA;;AACA,IAAIkD,cAAc,GAAGC,qBAAqB,CAAC,gBAAD,CAA1C;AACA5H,2BAAA,GAA8BA,MAAM,CAACC,OAAP,CAAe0C,QAAf,KAA4B,MAA5B,GACzB,CAAC,CAACgF,cAAD,IAAmBA,cAAc,CAACE,YAAnC,KAAoD,CAACpD,OAAO,CAACqD,SADpC,GAE1B,OAAOC,MAAP,KAAkB,WAFtB,EAIA;;AACA/H,mBAAA,GAAsBA,MAAM,CAACC,OAAP,CAAe0C,QAAf,KAA4B,SAA5B,GAClBqF,IAAI,CAACC,SAAL,CAAeC,mBADG,GAElBhG,kBAAkB,CAAC,IAAD,CAAlB,CAAyB7G,IAAzB,GAAgCiB,MAFpC;;AAIA,SAASsL,qBAAT,CAAgC5H,MAAhC,EAAwC;AACtC,MAAI;AACF,WAAOkC,kBAAkB,CAAClC,MAAD,CAAzB;AACD,GAFD,CAEE,OAAMxB,GAAN,EAAW;AACX,WAAO,IAAP;AACD;AACF;;;;;;;ACnCD;AACA;AACA;AACA;AACA;AACAwB,MAAM,CAACC,OAAP,GAAiB,8qFAAjB;;;;;;;ACLA;AACA,IAAIiC,kBAAkB,GAAGiG,IAAI,CACzB,sCACA,YADA,GAEA,+EAHyB,CAA7B;AAMAnI,MAAM,CAACC,OAAP,GAAiBiC,kBAAjB;;;;;;;;;ACPA;AACA;AACA;AACA;AAEA;AACA,IAAIA,kBAAkB,GAAGiG,IAAI,CACzB,qCACA,YADA,GAEA,gFAHyB,CAA7B;AAMA;AACA;AACA;AACA;;AACA,IAAIhG,mBAAmB,GAAG,0BAA1B,EAEA;AAEA;AACA;;AACA,IAAI3E,MAAM,GAAG;AACX4K,EAAAA,IAAI,EAAE,gBAAW,CAAE;AADR,CAAb;;AAGA,IAAI,OAAOJ,IAAP,KAAgB,WAAhB,IAA+B,OAAOlE,WAAP,KAAuB,UAAtD,IAAoE,OAAOH,gBAAP,KAA4B,UAApG,EAAgH;AAC9G;AACAnG,EAAAA,MAAM,CAACiG,EAAP,GAAY,UAAUC,KAAV,EAAiBjC,QAAjB,EAA2B;AACrCkC,IAAAA,gBAAgB,CAACD,KAAD,EAAQ,UAAU5B,OAAV,EAAmB;AACzCL,MAAAA,QAAQ,CAACK,OAAO,CAAC8B,IAAT,CAAR;AACD,KAFe,CAAhB;AAGD,GAJD;;AAKApG,EAAAA,MAAM,CAACqG,IAAP,GAAc,UAAU/B,OAAV,EAAmB;AAC/BgC,IAAAA,WAAW,CAAChC,OAAD,CAAX;AACD,GAFD;AAGD,CAVD,MAWK,IAAI,OAAO2C,OAAP,KAAmB,WAAvB,EAAoC;AACvC;AAEA,MAAIpC,aAAJ;;AACA,MAAI;AACFA,IAAAA,aAAa,GAAGH,kBAAkB,CAAC,gBAAD,CAAlC;AACD,GAFD,CAEE,OAAMhB,KAAN,EAAa;AACb,QAAI,QAAOA,KAAP,MAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAAvC,IAA+CA,KAAK,CAACuB,IAAN,KAAe,kBAAlE,EAAsF,CACpF;AACD,KAFD,MAEO;AACL,YAAMvB,KAAN;AACD;AACF;;AAED,MAAImB,aAAa;AACf;AACAA,EAAAA,aAAa,CAACgG,UAAd,KAA6B,IAF/B,EAEqC;AACnC,QAAIA,UAAU,GAAIhG,aAAa,CAACgG,UAAhC;AACA7K,IAAAA,MAAM,CAACqG,IAAP,GAAcwE,UAAU,CAACvE,WAAX,CAAuBnI,IAAvB,CAA4B0M,UAA5B,CAAd;AACA7K,IAAAA,MAAM,CAACiG,EAAP,GAAY4E,UAAU,CAAC5E,EAAX,CAAc9H,IAAd,CAAmB0M,UAAnB,CAAZ;AACD,GAND,MAMO;AACL7K,IAAAA,MAAM,CAACiG,EAAP,GAAYgB,OAAO,CAAChB,EAAR,CAAW9H,IAAX,CAAgB8I,OAAhB,CAAZ;AACAjH,IAAAA,MAAM,CAACqG,IAAP,GAAcY,OAAO,CAACZ,IAAR,CAAalI,IAAb,CAAkB8I,OAAlB,CAAd,CAFK,CAGL;;AACAjH,IAAAA,MAAM,CAACiG,EAAP,CAAU,YAAV,EAAwB,YAAY;AAClCgB,MAAAA,OAAO,CAAC2D,IAAR,CAAa,CAAb;AACD,KAFD;AAGA5K,IAAAA,MAAM,CAAC4K,IAAP,GAAc3D,OAAO,CAAC2D,IAAR,CAAazM,IAAb,CAAkB8I,OAAlB,CAAd;AACD;AACF,CA7BI,MA8BA;AACH,QAAM,IAAIlI,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,SAAS+L,YAAT,CAAsBpH,KAAtB,EAA6B;AAC3B,SAAO3G,MAAM,CAACgO,mBAAP,CAA2BrH,KAA3B,EAAkCsH,MAAlC,CAAyC,UAASC,OAAT,EAAkBxG,IAAlB,EAAwB;AACtE,WAAO1H,MAAM,CAACmO,cAAP,CAAsBD,OAAtB,EAA+BxG,IAA/B,EAAqC;AAC/CnC,MAAAA,KAAK,EAAEoB,KAAK,CAACe,IAAD,CADmC;AAE/C0G,MAAAA,UAAU,EAAE;AAFmC,KAArC,CAAP;AAID,GALM,EAKJ,EALI,CAAP;AAMD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,SAAT,CAAmB9I,KAAnB,EAA0B;AACxB,SAAOA,KAAK,IAAK,OAAOA,KAAK,CAAC1C,IAAb,KAAsB,UAAhC,IAAgD,OAAO0C,KAAK,SAAZ,KAAuB,UAA9E;AACD,EAED;;;AACAtC,MAAM,CAACH,OAAP,GAAiB,EAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAG,MAAM,CAACH,OAAP,CAAewL,GAAf,GAAqB,SAASA,GAAT,CAAa7H,EAAb,EAAiB8H,IAAjB,EAAuB;AAC1C,MAAIlK,CAAC,GAAG,IAAImK,QAAJ,CAAa,aAAa/H,EAAb,GAAkB,2BAA/B,CAAR;AACA,SAAOpC,CAAC,CAACoK,KAAF,CAAQpK,CAAR,EAAWkK,IAAX,CAAP;AACD,CAHD;AAKA;AACA;AACA;AACA;;;AACAtL,MAAM,CAACH,OAAP,CAAeA,OAAf,GAAyB,SAASA,OAAT,GAAmB;AAC1C,SAAO9C,MAAM,CAAC8K,IAAP,CAAY7H,MAAM,CAACH,OAAnB,CAAP;AACD,CAFD;;AAIA,IAAI4L,gBAAgB,GAAG,IAAvB;AAEAzL,MAAM,CAACiG,EAAP,CAAU,SAAV,EAAqB,UAAU0C,OAAV,EAAmB;AACtC,MAAIA,OAAO,KAAKhE,mBAAhB,EAAqC;AACnC,WAAO3E,MAAM,CAAC4K,IAAP,CAAY,CAAZ,CAAP;AACD;;AACD,MAAI;AACF,QAAIrM,MAAM,GAAGyB,MAAM,CAACH,OAAP,CAAe8I,OAAO,CAACpK,MAAvB,CAAb;;AAEA,QAAIA,MAAJ,EAAY;AACVkN,MAAAA,gBAAgB,GAAG9C,OAAO,CAACR,EAA3B,CADU,CAGV;;AACA,UAAI5E,MAAM,GAAGhF,MAAM,CAACiN,KAAP,CAAajN,MAAb,EAAqBoK,OAAO,CAACnK,MAA7B,CAAb;;AAEA,UAAI4M,SAAS,CAAC7H,MAAD,CAAb,EAAuB;AACrB;AACAA,QAAAA,MAAM,CACD3D,IADL,CACU,UAAU2D,MAAV,EAAkB;AACtBvD,UAAAA,MAAM,CAACqG,IAAP,CAAY;AACV8B,YAAAA,EAAE,EAAEQ,OAAO,CAACR,EADF;AAEV5E,YAAAA,MAAM,EAAEA,MAFE;AAGVG,YAAAA,KAAK,EAAE;AAHG,WAAZ;AAKA+H,UAAAA,gBAAgB,GAAG,IAAnB;AACD,SARL,WASW,UAAUzK,GAAV,EAAe;AACpBhB,UAAAA,MAAM,CAACqG,IAAP,CAAY;AACV8B,YAAAA,EAAE,EAAEQ,OAAO,CAACR,EADF;AAEV5E,YAAAA,MAAM,EAAE,IAFE;AAGVG,YAAAA,KAAK,EAAEoH,YAAY,CAAC9J,GAAD;AAHT,WAAZ;AAKAyK,UAAAA,gBAAgB,GAAG,IAAnB;AACD,SAhBL;AAiBD,OAnBD,MAoBK;AACH;AACAzL,QAAAA,MAAM,CAACqG,IAAP,CAAY;AACV8B,UAAAA,EAAE,EAAEQ,OAAO,CAACR,EADF;AAEV5E,UAAAA,MAAM,EAAEA,MAFE;AAGVG,UAAAA,KAAK,EAAE;AAHG,SAAZ;AAMA+H,QAAAA,gBAAgB,GAAG,IAAnB;AACD;AACF,KApCD,MAqCK;AACH,YAAM,IAAI1M,KAAJ,CAAU,qBAAqB4J,OAAO,CAACpK,MAA7B,GAAsC,GAAhD,CAAN;AACD;AACF,GA3CD,CA4CA,OAAOyC,GAAP,EAAY;AACVhB,IAAAA,MAAM,CAACqG,IAAP,CAAY;AACV8B,MAAAA,EAAE,EAAEQ,OAAO,CAACR,EADF;AAEV5E,MAAAA,MAAM,EAAE,IAFE;AAGVG,MAAAA,KAAK,EAAEoH,YAAY,CAAC9J,GAAD;AAHT,KAAZ;AAKD;AACF,CAvDD;AAyDA;AACA;AACA;AACA;;AACAhB,MAAM,CAAC0L,QAAP,GAAkB,UAAU7L,OAAV,EAAmB;AAEnC,MAAIA,OAAJ,EAAa;AACX,SAAK,IAAI4E,IAAT,IAAiB5E,OAAjB,EAA0B;AACxB,UAAIA,OAAO,CAAC8L,cAAR,CAAuBlH,IAAvB,CAAJ,EAAkC;AAChCzE,QAAAA,MAAM,CAACH,OAAP,CAAe4E,IAAf,IAAuB5E,OAAO,CAAC4E,IAAD,CAA9B;AACD;AACF;AACF;;AAEDzE,EAAAA,MAAM,CAACqG,IAAP,CAAY,OAAZ;AAED,CAZD;;AAcArG,MAAM,CAAC4L,IAAP,GAAc,UAAUrD,OAAV,EAAmB;AAC/B,MAAIkD,gBAAJ,EAAsB;AACpBzL,IAAAA,MAAM,CAACqG,IAAP,CAAY;AACV8B,MAAAA,EAAE,EAAEsD,gBADM;AAEVnD,MAAAA,OAAO,EAAE,IAFC;AAGVC,MAAAA,OAAO,EAAPA;AAHU,KAAZ;AAKD;AACF,CARD;;AAUA,IAAI,IAAJ,EAAoC;AAClC9F,EAAAA,WAAA,GAAczC,MAAM,CAAC0L,QAArB;AACAjJ,EAAAA,YAAA,GAAezC,MAAM,CAAC4L,IAAtB;AACD;;;;;;UCzMD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;ACtBA,IAAItP,WAAW,GAAGF,mBAAO,CAAC,GAAD,CAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;;;AACAqG,YAAA,GAAe,SAAS9C,IAAT,CAAcjD,MAAd,EAAsBC,OAAtB,EAA+B;AAC5C,MAAIF,IAAI,GAAGL,mBAAO,CAAC,GAAD,CAAlB;;AAEA,SAAO,IAAIK,IAAJ,CAASC,MAAT,EAAiBC,OAAjB,CAAP;AACD,CAJD;AAMA;AACA;AACA;AACA;;;AACA8F,cAAA,GAAiB,SAASzC,MAAT,CAAgBH,OAAhB,EAAyB;AACxC,MAAIG,MAAM,GAAG5D,mBAAO,CAAC,GAAD,CAApB;;AACA4D,EAAAA,MAAM,CAAC6L,GAAP,CAAWhM,OAAX;AACD,CAHD;AAKA;AACA;AACA;AACA;;;AACA4C,kBAAA,GAAqB,SAASqJ,UAAT,CAAoBvD,OAApB,EAA6B;AAChD,MAAIvI,MAAM,GAAG5D,mBAAO,CAAC,GAAD,CAApB;;AACA4D,EAAAA,MAAM,CAAC4L,IAAP,CAAYrD,OAAZ;AACD,CAHD;AAKA;AACA;AACA;AACA;;;AACA9F,0CAAA;AAEAA,gBAAA,GAAmBnG,WAAW,CAAC6I,QAA/B;AACA1C,oBAAA,GAAuBnG,WAAW,CAAC+N,YAAnC;AACA5H,YAAA,GAAenG,WAAW,CAACuB,IAA3B", "sources": ["webpack://workerpool/webpack/universalModuleDefinition", "webpack://workerpool/./src/Pool.js", "webpack://workerpool/./src/Promise.js", "webpack://workerpool/./src/WorkerHandler.js", "webpack://workerpool/./src/debug-port-allocator.js", "webpack://workerpool/./src/environment.js", "webpack://workerpool/./src/generated/embeddedWorker.js", "webpack://workerpool/./src/requireFoolWebpack.js", "webpack://workerpool/./src/worker.js", "webpack://workerpool/webpack/bootstrap", "webpack://workerpool/./src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"workerpool\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"workerpool\"] = factory();\n\telse\n\t\troot[\"workerpool\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", "var Promise = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers\n * @param {String} [script]   Optional worker script\n * @param {WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  this.workers = [];  // queue with all workers\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  this.debugPortStart = (options.debugPortStart || 43210);\n  this.nodeWorker = options.nodeWorker;\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n *\n * @param {String | Function} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Array} [params]  Function arguments applied when calling the function\n * @param {ExecOptions} [options]  Options object\n * @return {Promise.<*, Error>} result\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params]);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. The methods always return a promise.\n *\n * @return {Promise.<Object, Error>} proxy\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @protected\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @protected\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @protected\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @protected\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overridenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overridenParams.script || this.script, {\n    forkArgs: overridenParams.forkArgs || this.forkArgs,\n    forkOpts: overridenParams.forkOpts || this.forkOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n *\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  this.resolved = false;\n  this.rejected = false;\n  this.pending = true;\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolve, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @param {Function} onSuccess\n   * @param {Function} [onFail]\n   * @returns {Promise} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel te promise. This will reject the promise with a CancellationError\n   * @returns {Promise} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {Promise} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @param {Function} onFail\n * @returns {Promise} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @param {Function} fn\n * @returns {Promise} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nmodule.exports = Promise;\n", "'use strict';\n\nvar Promise = require('./Promise');\nvar environment = require('./environment');\nvar requireFoolWebpack = require('./requireFoolWebpack');\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * If sending `TERMINATE_METHOD_ID` does not cause the child process to exit in this many milliseconds,\n * force-kill the child process.\n */\nvar CHILD_PROCESS_EXIT_TIMEOUT = 1000;\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, Worker) {\n  // create the web worker\n  var worker = new Worker(script);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message) {\n    this.postMessage(message);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads) {\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: false, // automatically pipe worker.STDOUT to process.STDOUT\n    stderr: false  // automatically pipe worker.STDERR to process.STDERR\n  });\n  worker.isWorkerThread = true;\n  // make the worker mimic a child_process\n  worker.send = function(message) {\n    this.postMessage(message);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv)\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {WorkerPoolOptions} _options See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n\n  this.terminating = false;\n  this.terminated = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    id: id,\n    method: method,\n    params: params\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      // terminate worker\n      return me.terminateAndNotify(true)\n        .then(function() {\n          throw error;\n        }, function(err) {\n          throw err;\n        });\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is working or not\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n    this.processing = Object.create(null);\n  }\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        if (this.worker.isChildProcess) {\n          var cleanExitTimeout = setTimeout(function() {\n            if (me.worker) {\n              me.worker.kill();\n            }\n          }, CHILD_PROCESS_EXIT_TIMEOUT);\n\n          this.worker.once('exit', function() {\n            clearTimeout(cleanExitTimeout);\n            if (me.worker) {\n              me.worker.killed = true;\n            }\n            cleanup();\n          });\n\n          if (this.worker.ready) {\n            this.worker.send(TERMINATE_METHOD_ID);\n          } else {\n            this.worker.requestQueue.push(TERMINATE_METHOD_ID)\n          }\n        } else {\n          // worker_thread\n          this.worker.kill();\n          this.worker.killed = true;\n          cleanup();\n        }\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout = timeout;\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "var requireFoolWebpack = require('./requireFoolWebpack');\n\n// source: https://github.com/flexdinesh/browser-or-node\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = tryRequireFoolWebpack('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : requireFoolWebpack('os').cpus().length;\n\nfunction tryRequireFoolWebpack (module) {\n  try {\n    return requireFoolWebpack(module);\n  } catch(err) {\n    return null\n  }\n}\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(){var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(r){return(_typeof=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&\\\"function\\\"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?\\\"symbol\\\":typeof r})(r)}var requireFoolWebpack=eval(\\\"typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \\\\\\\" + module + \\\\\\\" not found.') }\\\"),TERMINATE_METHOD_ID=\\\"__workerpool-terminate__\\\",worker={exit:function(){}},WorkerThreads,parentPort;if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)worker.on=function(r,e){addEventListener(r,function(r){e(r.data)})},worker.send=function(r){postMessage(r)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");try{WorkerThreads=requireFoolWebpack(\\\"worker_threads\\\")}catch(error){if(\\\"object\\\"!==_typeof(error)||null===error||\\\"MODULE_NOT_FOUND\\\"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=process.send.bind(process),worker.on(\\\"disconnect\\\",function(){process.exit(1)}),worker.exit=process.exit.bind(process))}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(r,e){return Object.defineProperty(r,e,{value:o[e],enumerable:!0})},{})}function isPromise(r){return r&&\\\"function\\\"==typeof r.then&&\\\"function\\\"==typeof r.catch}worker.methods={},worker.methods.run=function(r,e){r=new Function(\\\"return (\\\"+r+\\\").apply(null, arguments);\\\");return r.apply(r,e)},worker.methods.methods=function(){return Object.keys(worker.methods)};var currentRequestId=null;worker.on(\\\"message\\\",function(e){if(e===TERMINATE_METHOD_ID)return worker.exit(0);try{var r=worker.methods[e.method];if(!r)throw new Error('Unknown method \\\"'+e.method+'\\\"');currentRequestId=e.id;var o=r.apply(r,e.params);isPromise(o)?o.then(function(r){worker.send({id:e.id,result:r,error:null}),currentRequestId=null}).catch(function(r){worker.send({id:e.id,result:null,error:convertError(r)}),currentRequestId=null}):(worker.send({id:e.id,result:o,error:null}),currentRequestId=null)}catch(r){worker.send({id:e.id,result:null,error:convertError(r)})}}),worker.register=function(r){if(r)for(var e in r)r.hasOwnProperty(e)&&(worker.methods[e]=r[e]);worker.send(\\\"ready\\\")},worker.emit=function(r){currentRequestId&&worker.send({id:currentRequestId,isEvent:!0,payload:r})},__webpack_unused_export__=worker.register,worker.emit}()}();\";\n", "// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\' ' +\n    '? require ' +\n    ': function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\nmodule.exports = requireFoolWebpack;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n  } else {\n    worker.on = process.on.bind(process);\n    worker.send = process.send.bind(process);\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.exit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              worker.send({\n                id: request.id,\n                result: result,\n                error: null\n              });\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        worker.send({\n          id: request.id,\n          result: result,\n          error: null\n        });\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} methods\n */\nworker.register = function (methods) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  worker.send('ready');\n\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "var environment = require('./environment');\n\n/**\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nexports.pool = function pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {Object} [methods]\n */\nexports.worker = function worker(methods) {\n  var worker = require('./worker');\n  worker.add(methods);\n};\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nexports.workerEmit = function workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\n\n/**\n * Create a promise.\n * @type {Promise} promise\n */\nexports.Promise = require('./Promise');\n\nexports.platform = environment.platform;\nexports.isMainThread = environment.isMainThread;\nexports.cpus = environment.cpus;"], "names": ["Promise", "require", "Worker<PERSON><PERSON>ler", "environment", "DebugPortAllocator", "DEBUG_PORT_ALLOCATOR", "Pool", "script", "options", "workers", "tasks", "forkArgs", "Object", "freeze", "forkOpts", "debugPortStart", "nodeWorker", "workerType", "maxQueueSize", "Infinity", "onCreateWorker", "onTerminateWorker", "validateMaxWorkers", "maxWorkers", "Math", "max", "cpus", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "ensureWorkerThreads", "prototype", "exec", "method", "params", "Array", "isArray", "TypeError", "resolver", "defer", "length", "Error", "task", "timeout", "push", "originalTimeout", "promise", "delay", "indexOf", "call", "String", "proxy", "arguments", "pool", "then", "methods", "for<PERSON>ach", "slice", "worker", "_get<PERSON><PERSON><PERSON>", "me", "shift", "pending", "terminated", "_remove<PERSON><PERSON>ker", "i", "busy", "_createWorkerHandler", "releasePort", "debugPort", "_removeWorkerFromList", "resolve", "reject", "terminate", "err", "index", "splice", "force", "f", "removeW<PERSON>ker", "promises", "termPromise", "terminateAndNotify", "always", "all", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overridenParams", "nextAvailableStartingAt", "isNumber", "isInteger", "value", "round", "module", "exports", "handler", "parent", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "_process", "onSuccess", "onFail", "s", "_then", "_resolve", "result", "fn", "_reject", "error", "cancel", "CancellationError", "timer", "setTimeout", "TimeoutError", "clearTimeout", "callback", "res", "remaining", "results", "p", "message", "stack", "constructor", "name", "requireFoolWebpack", "TERMINATE_METHOD_ID", "CHILD_PROCESS_EXIT_TIMEOUT", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "code", "getDefaultWorker", "platform", "Blob", "window", "URL", "createObjectURL", "blob", "type", "__dirname", "setupWorker", "setupBrowserWorker", "setupWorkerThreadWorker", "setupProcessWorker", "resolveForkOptions", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "postMessage", "stdout", "stderr", "isWorkerThread", "kill", "disconnect", "child_process", "fork", "isChildProcess", "opts", "processExecArgv", "process", "execArgv", "join", "inspectorActive", "debugBrk", "arg", "assign", "concat", "objectToError", "obj", "temp", "props", "keys", "_options", "ready", "requestQueue", "response", "dispatchQueuedRequests", "id", "processing", "undefined", "isEvent", "payload", "terminating", "onError", "create", "request", "exitCode", "signalCode", "spawnargs", "spawnfile", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "MAX_PORTS", "ports", "starting", "port", "isNode", "nodeProcess", "versions", "node", "worker_threads", "tryRequireFoolWebpack", "isMainThread", "connected", "Window", "self", "navigator", "hardwareConcurrency", "eval", "exit", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "defineProperty", "enumerable", "isPromise", "run", "args", "Function", "apply", "currentRequestId", "register", "hasOwnProperty", "emit", "add", "workerEmit"], "sourceRoot": ""}