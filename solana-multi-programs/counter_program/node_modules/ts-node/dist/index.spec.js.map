{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,6BAA6B;AAC7B,+CAAoC;AACpC,6BAA2B;AAC3B,+BAAiC;AACjC,+BAAiC;AACjC,uCAAyC;AACzC,iCAA2C;AAE3C,IAAM,OAAO,GAAG,WAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAC3C,IAAM,SAAS,GAAG,WAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;AAChD,IAAM,QAAQ,GAAG,YAAS,SAAS,uBAAgB,OAAO,qBAAiB,CAAA;AAE3E,IAAM,iBAAiB,GAAG,gFAAgF,CAAA;AAE1G,QAAQ,CAAC,SAAS,EAAE;IAClB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEnB,EAAE,CAAC,mCAAmC,EAAE;QACtC,aAAM,CAAC,eAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,KAAK,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;YACrC,oBAAI,CAAI,QAAQ,uBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACzD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yBAAyB,EAAE,UAAU,IAAI;YAC1C,oBAAI,CAAC,oCAAoC,EAAE;gBACzC,GAAG,EAAE,OAAO;aACb,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAI,QAAQ,WAAK,WAAI,CAAC,OAAO,EAAE,aAAa,CAAC,OAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACzE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;YACvC,oBAAI,CAAI,QAAQ,sEAAiE,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,iBAAiB,EAAE,UAAU,IAAI;gBAClC,oBAAI,CACF;oBACE,QAAQ;oBACR,6BAA6B;oBAC7B,2DAA2D;iBAC5D,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CACF;oBACE,QAAQ;oBACR,6BAA6B;oBAC7B,uEAAuE;iBACxE,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,kBAAkB,EAAE,UAAU,IAAI;YACnC,oBAAI,CACC,QAAQ,+EAA0E,EACrF,UAAU,GAAG,EAAE,MAAM;gBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,2BAA2B,EAAE,UAAU,IAAI;YAC5C,oBAAI,CAAI,QAAQ,mCAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBACnE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAE3B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;YACtC,oBAAI,CAAI,QAAQ,4EAAuE,EAAE,UAAU,GAAG;gBACpG,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CACrC,8CAA8C;oBAC9C,sDAAsD,CACvD,CAAC,CAAA;gBAEF,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qCAAqC,EAAE,UAAU,IAAI;YACtD,oBAAI,CACC,QAAQ,qGAAgG,EAC3G,UAAU,GAAG;gBACX,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1B,iGAAiG,CAClG,CAAA;gBAED,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAI,QAAQ,iBAAc,EAAE,UAAU,GAAG;gBAC3C,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC1B,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,OAAI;oBAC3C,kDAAkD;oBAClD,oBAAoB;oBACpB,uBAAuB;iBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,IAAI,CAAC,mCAAmC,EAAE,UAAU,IAAI;YACzD,oBAAI,CAAI,QAAQ,mCAA8B,EAAE,UAAU,GAAG;gBAC3D,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC1B,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,OAAI;oBAC3C,kDAAkD;oBAClD,oBAAoB;iBACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,oCAAoC,EAAE,UAAU,IAAI;YACrD,oBAAI,CAAI,QAAQ,8BAAyB,EAAE,UAAU,GAAG;gBACtD,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;YAC1D,IAAM,EAAE,GAAG,oBAAI,CAAC,QAAQ,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC7C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBAElC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;YAC7C,IAAM,EAAE,GAAG,oBAAI,CAAI,QAAQ,QAAK,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;YAClD,IAAM,EAAE,GAAG,oBAAI,CAAI,QAAQ,qCAAkC,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBAEtC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAI,QAAQ,0DAAqD,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC1F,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;gBAE9D,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,UAAU,IAAI;YAC3D,oBAAI,CAAI,QAAQ,iDAA4C,EAAE,UAAU,GAAG,EAAE,MAAM;gBACjF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,IAAI,CAAC,uCAAuC,EAAE,UAAU,IAAI;YAC7D,oBAAI,CAAI,QAAQ,qDAAkD,EAAE,UAAU,GAAG,EAAE,MAAM;gBACvF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAE/B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE,UAAU,IAAI;YAC9C,oBAAI,CAAI,QAAQ,wBAAqB,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC1D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,UAAU,EAAE;QACnB,gBAAQ,CAAC;YACP,OAAO,EAAE,WAAI,CAAC,OAAO,EAAE,eAAe,CAAC;YACvC,eAAe,EAAE;gBACf,GAAG,EAAE,UAAU;aAChB;SACF,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAM,CAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAA;YAEpC,aAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;YAErC,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,CAAC,GAAG,UAAU,CAAC,kBAAkB,EAAE;gBACvC,WAAW,EAAE,OAAO;aACrB,CAAC,CAAA;YAEF,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;YACzC,IAAI;gBACF,OAAO,CAAC,gBAAgB,CAAC,CAAA;aAC1B;YAAC,OAAO,KAAK,EAAE;gBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,uBAAuB;oBACvB,qBAAmB,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,WAAQ;iBAChE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,IAAI,EAAE,CAAA;aACP;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,cAAc,EAAE;YACvB,IAAI,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YACpC,IAAI,QAAgB,CAAA;YAEpB,MAAM,CAAC;gBAAA,iBAWN;gBAVC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAC,CAAM,EAAE,QAAQ;oBAC5C,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;oBAE3B,CAAC,CAAC,QAAQ,GAAG,UAAC,IAAY,EAAE,QAAgB;wBAC1C,QAAQ,GAAG,IAAI,CAAA;wBACf,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBAC5C,CAAC,CAAA;oBAED,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;gBACzB,CAAC,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,KAAK,CAAC;gBACJ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;YAClC,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;gBACzC,IAAI;oBACF,OAAO,CAAC,uBAAuB,CAAC,CAAA;iBACjC;gBAAC,OAAO,KAAK,EAAE;oBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAA;iBACpE;gBAED,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE5C,IAAI,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "sourcesContent": ["import { expect } from 'chai'\nimport { exec } from 'child_process'\nimport { join } from 'path'\nimport semver = require('semver')\nimport ts = require('typescript')\nimport proxyquire = require('proxyquire')\nimport { register, VERSION } from './index'\n\nconst testDir = join(__dirname, '../tests')\nconst EXEC_PATH = join(__dirname, '../dist/bin')\nconst BIN_EXEC = `node \"${EXEC_PATH}\" --project \"${testDir}/tsconfig.json\"`\n\nconst SOURCE_MAP_REGEXP = /\\/\\/# sourceMappingURL=data:application\\/json;charset=utf\\-8;base64,[\\w\\+]+=*$/\n\ndescribe('ts-node', function () {\n  this.timeout(10000)\n\n  it('should export the correct version', function () {\n    expect(VERSION).to.equal(require('../package.json').version)\n  })\n\n  describe('cli', function () {\n    this.slow(1000)\n\n    it('should execute cli', function (done) {\n      exec(`${BIN_EXEC} tests/hello-world`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should register via cli', function (done) {\n      exec(`node -r ../register hello-world.ts`, {\n        cwd: testDir\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should execute cli with absolute path', function (done) {\n      exec(`${BIN_EXEC} \"${join(testDir, 'hello-world')}\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should print scripts', function (done) {\n      exec(`${BIN_EXEC} -p \"import { example } from './tests/complex/index';example()\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('example\\n')\n\n        return done()\n      })\n    })\n\n    if (semver.gte(ts.version, '1.8.0')) {\n      it('should allow js', function (done) {\n        exec(\n          [\n            BIN_EXEC,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-p \"import { main } from \\'./tests/allow-js/run\\';main()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n\n      it('should include jsx when `allow-js` true', function (done) {\n        exec(\n          [\n            BIN_EXEC,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-p \"import { Foo2 } from \\'./tests/allow-js/with-jsx\\'; Foo2.sayHi()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n    }\n\n    it('should eval code', function (done) {\n      exec(\n        `${BIN_EXEC} -e \"import * as m from './tests/module';console.log(m.example('test'))\"`,\n        function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('TEST\\n')\n\n          return done()\n        }\n      )\n    })\n\n    it('should import empty files', function (done) {\n      exec(`${BIN_EXEC} -e \"import './tests/empty'\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('')\n\n        return done()\n      })\n    })\n\n    it('should throw errors', function (done) {\n      exec(`${BIN_EXEC} -e \"import * as m from './tests/module';console.log(m.example(123))\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.match(new RegExp(\n          'TS2345: Argument of type \\'(?:number|123)\\' ' +\n          'is not assignable to parameter of type \\'string\\'\\\\.'\n        ))\n\n        return done()\n      })\n    })\n\n    it('should be able to ignore diagnostic', function (done) {\n      exec(\n        `${BIN_EXEC} --ignoreDiagnostics 2345 -e \"import * as m from './tests/module';console.log(m.example(123))\"`,\n        function (err) {\n          if (err === null) {\n            return done('Command was expected to fail, but it succeeded.')\n          }\n\n          expect(err.message).to.match(\n            /TypeError: (?:(?:undefined|foo\\.toUpperCase) is not a function|.*has no method \\'toUpperCase\\')/\n          )\n\n          return done()\n        }\n      )\n    })\n\n    it('should work with source maps', function (done) {\n      exec(`${BIN_EXEC} tests/throw`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:3`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^',\n          'Error: this is a demo'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it.skip('eval should work with source maps', function (done) {\n      exec(`${BIN_EXEC} -p \"import './tests/throw'\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:3`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('should support transpile only mode', function (done) {\n      exec(`${BIN_EXEC} --transpileOnly -p \"x\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('ReferenceError: x is not defined')\n\n        return done()\n      })\n    })\n\n    it('should pipe into `ts-node` and evaluate', function (done) {\n      const cp = exec(BIN_EXEC, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('hello\\n')\n\n        return done()\n      })\n\n      cp.stdin.end(\"console.log('hello')\")\n    })\n\n    it('should pipe into `ts-node`', function (done) {\n      const cp = exec(`${BIN_EXEC} -p`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('true\\n')\n\n        return done()\n      })\n\n      cp.stdin.end('true')\n    })\n\n    it('should pipe into an eval script', function (done) {\n      const cp = exec(`${BIN_EXEC} --fast -p 'process.stdin.isTTY'`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('undefined\\n')\n\n        return done()\n      })\n\n      cp.stdin.end('true')\n    })\n\n    it('should support require flags', function (done) {\n      exec(`${BIN_EXEC} -r ./tests/hello-world -p \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\nsuccess\\nundefined\\n')\n\n        return done()\n      })\n    })\n\n    it('should support require from node modules', function (done) {\n      exec(`${BIN_EXEC} -r typescript -e \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('success\\n')\n\n        return done()\n      })\n    })\n\n    it.skip('should use source maps with react tsx', function (done) {\n      exec(`${BIN_EXEC} -r ./tests/emit-compiled.ts tests/jsx-react.tsx`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('todo')\n\n        return done()\n      })\n    })\n\n    it('should allow custom typings', function (done) {\n      exec(`${BIN_EXEC} tests/custom-types`, function (err, stdout) {\n        expect(err).to.match(/Error: Cannot find module 'does-not-exist'/)\n\n        return done()\n      })\n    })\n  })\n\n  describe('register', function () {\n    register({\n      project: join(testDir, 'tsconfig.json'),\n      compilerOptions: {\n        jsx: 'preserve'\n      }\n    })\n\n    it('should be able to require typescript', function () {\n      const m = require('../tests/module')\n\n      expect(m.example('foo')).to.equal('FOO')\n    })\n\n    it('should compile through js and ts', function () {\n      const m = require('../tests/complex')\n\n      expect(m.example()).to.equal('example')\n    })\n\n    it('should work with proxyquire', function () {\n      const m = proxyquire('../tests/complex', {\n        './example': 'hello'\n      })\n\n      expect(m.example()).to.equal('hello')\n    })\n\n    it('should use source maps', function (done) {\n      try {\n        require('../tests/throw')\n      } catch (error) {\n        expect(error.stack).to.contain([\n          'Error: this is a demo',\n          `    at Foo.bar (${join(__dirname, '../tests/throw.ts')}:3:18)`\n        ].join('\\n'))\n\n        done()\n      }\n    })\n\n    describe('JSX preserve', () => {\n      let old = require.extensions['.tsx']\n      let compiled: string\n\n      before(function () {\n        require.extensions['.tsx'] = (m: any, fileName) => {\n          const _compile = m._compile\n\n          m._compile = (code: string, fileName: string) => {\n            compiled = code\n            return _compile.call(this, code, fileName)\n          }\n\n          return old(m, fileName)\n        }\n      })\n\n      after(function () {\n        require.extensions['.tsx'] = old\n      })\n\n      it('should use source maps', function (done) {\n        try {\n          require('../tests/with-jsx.tsx')\n        } catch (error) {\n          expect(error.stack).to.contain('SyntaxError: Unexpected token <\\n')\n        }\n\n        expect(compiled).to.match(SOURCE_MAP_REGEXP)\n\n        done()\n      })\n    })\n  })\n})\n"]}