{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6BAA0E;AAC1E,yBAAgD;AAChD,yBAAyC;AACzC,qDAAuD;AACvD,+BAAiC;AACjC,+BAAiC;AACjC,uBAAyB;AACzB,+BAAiC;AACjC,wCAA0C;AAC1C,yCAAsC;AACtC,2BAA4B;AAG5B;;GAEG;AACU,QAAA,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAA;AAE9D;;GAEG;AACH,IAAM,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;AACjD,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,cAAM,OAAA,SAAS,EAAT,CAAS,CAAA;AAClF,IAAM,OAAO,GAAG,WAAW,CAAC,CAAC;IAC3B,UAAQ,GAAW,EAAE,EAAiB;QACpC,OAAO,UAAC,CAAI;YACV,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YACb,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;QACd,CAAC,CAAA;IACH,CAAC,CAAC,CAAC;IACH,UAAQ,CAAS,EAAE,EAAiB,IAAK,OAAA,EAAE,EAAF,CAAE,CAAA;AAwB7C;;GAEG;AACU,QAAA,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAA;AAyCzD;;GAEG;AACU,QAAA,QAAQ,GAAY;IAC/B,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACvC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC1D,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACzC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;IACtD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IACzC,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC/D,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC5C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;IACvC,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAClD,WAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpD,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IACnE,SAAS,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAChD,aAAa,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;CACzD,CAAA;AAED;;GAEG;AACH,IAAM,wBAAwB,GAAG;IAC/B,SAAS,EAAE,IAAI;IACf,eAAe,EAAE,KAAK;IACtB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,KAAK;IAClB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,aAAa;CACtB,CAAA;AAED;;GAEG;AACH,eAAuB,KAAyB;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AACtE,CAAC;AAFD,sBAEC;AAED;;GAEG;AACH,eAAuB,KAAyB;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AAClE,CAAC;AAFD,sBAEC;AAED;;GAEG;AACH,0BAAkC,KAAa;IAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AAClC,CAAC;AAFD,4CAEC;AAED;;GAEG;AACH;IAA6B,2BAAS;IAGpC,iBAAoB,cAAsB,EAAS,eAAyB;QAA5E,YACE,kBAAM,2CAAoC,cAAgB,CAAC,SAC5D;QAFmB,oBAAc,GAAd,cAAc,CAAQ;QAAS,qBAAe,GAAf,eAAe,CAAU;QAF5E,UAAI,GAAG,SAAS,CAAA;;IAIhB,CAAC;IAED;;OAEG;IACH,kBAAC,sBAAc,CAAC,GAAhB;QACE,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IACH,cAAC;AAAD,CAAC,AAbD,CAA6B,sBAAS,GAarC;AAbY,0BAAO;AA2BpB;;GAEG;AACH;IACE,IAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAO,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAEhF,OAAO,WAAI,CAAC,WAAM,EAAE,EAAE,aAAW,IAAM,CAAC,CAAA;AAC1C,CAAC;AAED;;GAEG;AACH,kBAA0B,IAAkB;IAAlB,qBAAA,EAAA,SAAkB;IAC1C,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAQ,EAAE,IAAI,CAAC,CAAA;IACjD,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,SAAS,EAAE,CAAA;IAC5D,IAAM,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IAEnD,IAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC;QACjE,IAAI;QACJ,KAAK;QACL,KAAK,CAAC,yCAAyC;KAChD,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAEd,IAAM,WAAW,GAAgB;QAC/B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAC7B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAC7B,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;KAC7B,CAAA;IAED,IAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAC7C,OAAO,CAAC,MAAM,IAAI,gBAAgB,CACnC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,IAAI,MAAM,CAAC,GAAG,CAAC,EAAf,CAAe,CAAC,CAAA;IAE7B,yDAAyD;IACzD,gBAAgB,CAAC,OAAO,CAAC;QACvB,WAAW,EAAE,MAAM;QACnB,YAAY,YAAE,IAAY;YACxB,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAClC,CAAC;KACF,CAAC,CAAA;IAEF,qDAAqD;IACrD,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IACjB,IAAA,yCAAe,EAAE,yBAAO,EAAE,iCAAW,CAAY;IACzD,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAA;IACjD,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,IAAI,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,CAAA;IAC9E,IAAM,EAAE,GAAe,OAAO,CAAC,QAAQ,CAAC,CAAA;IACxC,IAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,SAAS,CAAA;IACtD,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAA;IACpD,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAA;IAC1D,IAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;IAC/F,IAAM,oBAAoB,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;IAChF,IAAM,UAAU,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IAClC,IAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAA;IAEvD,IAAM,QAAQ,GAAG,WAAI,CACnB,cAAO,CAAC,GAAG,EAAE,cAAc,CAAC,EAC5B,iBAAiB,CAAC;QAChB,OAAO,EAAE,EAAE,CAAC,OAAO;QACnB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,SAAS,WAAA;QACT,SAAS,WAAA;QACT,iBAAiB,mBAAA;QACjB,QAAQ,UAAA;KACT,CAAC,CACH,CAAA;IAED,IAAM,cAAc,GAA8B;QAChD,UAAU,EAAE,cAAM,OAAA,QAAG,EAAH,CAAG;QACrB,mBAAmB,EAAE,cAAM,OAAA,GAAG,EAAH,CAAG;QAC9B,oBAAoB,EAAE,UAAC,IAAI,IAAK,OAAA,IAAI,EAAJ,CAAI;KACrC,CAAA;IAED,IAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM;QACtC,CAAC,CAAC,EAAE,CAAC,oCAAoC;QACzC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAA;IAExB,uBAAwB,WAA0C;QAChE,IAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QACrE,IAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAA;QACpD,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,eAAe,CAAC,CAAA;IACrD,CAAC;IAED,uDAAuD;IACvD,IAAI,oBAAoB,CAAC,MAAM;QAAE,MAAM,aAAa,CAAC,oBAAoB,CAAC,CAAA;IAE1E,qCAAqC;IACrC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;QAC1B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACtB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;KACxB;IAED,iDAAiD;IACjD,KAAmB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS;QAAvB,IAAM,IAAI,kBAAA;QAAe,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KAAA;IAE5D;;OAEG;IACH,IAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,UAAC,IAAY,IAAK,OAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAvC,CAAuC,CAAC,CAAC,CAAC;QAC7D,CAAC,UAAC,CAAS,IAAK,OAAA,KAAK,EAAL,CAAK,CAAC,CAAA;IAExB;;OAEG;IACH,IAAI,SAAS,GAAG,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAc;QAAd,2BAAA,EAAA,cAAc;QACtE,IAAM,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE;YACtC,QAAQ,UAAA;YACR,YAAY,cAAA;YACZ,eAAe,EAAE,MAAM,CAAC,OAAO;YAC/B,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAA;QAEF,IAAM,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;YACzC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC;YAC1D,EAAE,CAAA;QAEJ,IAAI,cAAc,CAAC,MAAM;YAAE,MAAM,aAAa,CAAC,cAAc,CAAC,CAAA;QAE9D,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,aAAuB,CAAC,CAAA;IAC5D,CAAC,CAAA;IAED,IAAI,WAAW,GAAG,UAAU,KAAa,EAAE,SAAiB,EAAE,SAAiB;QAC7E,MAAM,IAAI,SAAS,CAAC,0DAAwD,CAAC,CAAA;IAC/E,CAAC,CAAA;IAED,+DAA+D;IAC/D,IAAI,SAAS,EAAE;QACb,oCAAoC;QACpC,IAAM,mBAAiB,GAAG,UAAU,IAAY,EAAE,QAAgB;YAChE,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBAC3C,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;gBACrC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;aAC3E;QACH,CAAC,CAAA;QAED,8CAA8C;QAC9C,IAAM,WAAW,GAAG;YAClB,kBAAkB,EAAE,cAAM,OAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAjC,CAAiC;YAC3D,gBAAgB,EAAE,UAAC,QAAgB;gBACjC,IAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBAE9C,kFAAkF;gBAClF,uFAAuF;gBACvF,mFAAmF;gBACnF,uFAAuF;gBACvF,mFAAmF;gBACnF,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,SAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC7E,CAAC;YACD,iBAAiB,YAAE,QAAgB;gBACjC,8CAA8C;gBAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBACzE,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAA;iBACpD;gBAED,IAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;gBAC/C,IAAI,QAAQ,KAAK,SAAS;oBAAE,OAAM;gBAClC,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;YAC/C,CAAC;YACD,UAAU,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;YAC7C,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC;YACvC,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC;YAC7D,cAAc,EAAE,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC;YAChE,eAAe,EAAE,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC;YACnE,UAAU,EAAE,cAAM,OAAA,QAAG,EAAH,CAAG;YACrB,mBAAmB,EAAE,cAAM,OAAA,GAAG,EAAH,CAAG;YAC9B,sBAAsB,EAAE,cAAM,OAAA,MAAM,CAAC,OAAO,EAAd,CAAc;YAC5C,qBAAqB,EAAE,cAAM,OAAA,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAxC,CAAwC;YACrE,qBAAqB,EAAE,cAAM,OAAA,YAAY,EAAZ,CAAY;SAC1C,CAAA;QAED,IAAM,SAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;QAErD,SAAS,GAAG,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAsB;YAAtB,2BAAA,EAAA,cAAsB;YAC1E,wDAAwD;YACxD,mBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAEjC,IAAM,MAAM,GAAG,SAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;YAE9C,iFAAiF;YACjF,IAAM,WAAW,GAAG,SAAO,CAAC,6BAA6B,EAAE;iBACxD,MAAM,CAAC,SAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;iBACjD,MAAM,CAAC,SAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAA;YAEnD,IAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;YAExE,IAAI,cAAc,CAAC,MAAM;gBAAE,MAAM,aAAa,CAAC,cAAc,CAAC,CAAA;YAE9D,IAAI,MAAM,CAAC,WAAW,EAAE;gBACtB,MAAM,IAAI,SAAS,CAAI,eAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,mBAAgB,CAAC,CAAA;aAChE;YAED,+CAA+C;YAC/C,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnC,MAAM,IAAI,SAAS,CACjB,mCAAmC;oBACnC,kEAAkE;oBAClE,0EAA0E;oBAC1E,yDAAyD;qBACzD,MAAK,eAAQ,CAAC,QAAQ,CAAC,OAAK,CAAA,CAC7B,CAAA;aACF;YAED,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACjE,CAAC,CAAA;QAED,WAAW,GAAG,UAAU,IAAY,EAAE,QAAgB,EAAE,QAAgB;YACtE,mBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAEjC,IAAM,IAAI,GAAG,SAAO,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YAC/D,IAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YACnE,IAAM,OAAO,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YAEvE,OAAO,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,CAAA;QAC1B,CAAC,CAAA;KACF;IAED,IAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,KAAK,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;IACnG,IAAM,QAAQ,GAAa,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,WAAW,aAAA,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IAElF,2BAA2B;IAC3B,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;QAC1B,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAA;IACnE,CAAC,CAAC,CAAA;IAEF,OAAO,QAAQ,CAAA;AACjB,CAAC;AAtND,4BAsNC;AAED;;GAEG;AACH,sBAAuB,QAAgB,EAAE,MAAgB;IACvD,IAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;IAE1C,OAAO,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAA;AAC1C,CAAC;AAED;;GAEG;AACH,2BACE,GAAW,EACX,MAAgB,EAChB,QAAkB,EAClB,eAAyD;IAEzD,IAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,eAAe,CAAA;IAEtD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAM,EAAE,QAAQ;QAClD,IAAI,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;YAClC,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;SACxB;QAED,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;QAE3B,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAY,EAAE,QAAgB;YACnD,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;YAElC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAA;QACxE,CAAC,CAAA;QAED,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;IACzB,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,mBAAoB,EAAY,EAAE,MAA6B;IAC7D,sDAAsD;IACtD,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAA;IACzB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAA;IAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,CAAA;IAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA;IACpC,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA;IACpC,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAA;IAEzC,iDAAiD;IACjD,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAA;KAC5C;IAED,qGAAqG;IACrG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAA;KAC/C;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED;;GAEG;AACH,oBACE,GAAW,EACX,EAAY,EACZ,UAAqC,EACrC,QAA8C,EAC9C,eAAwB,EACxB,OAAuB,EACvB,SAA0B;IAE1B,IAAI,MAAM,GAAG,EAAE,eAAe,EAAE,EAAE,EAAE,CAAA;IACpC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAA;IACpC,IAAI,cAAc,GAAuB,SAAS,CAAA;IAElD,6CAA6C;IAC7C,IAAI,CAAC,SAAS,EAAE;QACd,cAAc,GAAG,OAAO;YACtB,CAAC,CAAC,gBAAgB,CAAC,cAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACzC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,CAAA;QAExD,IAAI,cAAc,EAAE;YAClB,IAAM,MAAM,GAAG,EAAE,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAA;YAE1D,sBAAsB;YACtB,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,OAAO,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAA;aAC9D;YAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;YACtB,QAAQ,GAAG,gBAAgB,CAAC,cAAO,CAAC,cAAc,CAAC,CAAC,CAAA;SACrD;KACF;IAED,6DAA6D;IAC7D,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,eAAe,EAAE,wBAAwB,CAAC,CAAA;IAE7G,OAAO,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,0BAA0B,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,CAAA;AAC1G,CAAC;AAOD;;GAEG;AACH,qBACE,QAAgB,EAChB,WAAoB,EACpB,WAAwB,EACxB,OAA8E,EAC9E,YAA0C;IAE1C,IAAI,WAAW,KAAK,KAAK,EAAE;QACzB,OAAO,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAmB;YAClE,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;YAExB,IAAA,wCAAwD,EAAvD,aAAK,EAAE,iBAAS,CAAuC;YAC9D,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;YAErE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAA;YAEtC,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;KACF;IAED,0DAA0D;IAC1D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAErB,OAAO,UAAU,IAAY,EAAE,QAAgB,EAAE,UAAmB;QAClE,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;QAE9B,IAAM,SAAS,GAAG,WAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC9D,IAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;QACxC,IAAM,UAAU,GAAG,KAAG,SAAS,GAAG,SAAW,CAAA;QAE7C,IAAI;YACF,IAAM,QAAM,GAAG,iBAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YAC/C,IAAI,mBAAmB,CAAC,QAAM,CAAC,EAAE;gBAC/B,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAM,CAAA;gBACtC,OAAO,QAAM,CAAA;aACd;SACF;QAAC,OAAO,GAAG,EAAE,EAAC,aAAa,EAAC;QAEvB,IAAA,wCAAwD,EAAvD,aAAK,EAAE,iBAAS,CAAuC;QAC9D,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;QAErE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAA;QACtC,kBAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;QAEjC,OAAO,MAAM,CAAA;IACf,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,sBAAuB,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,YAA0C;IACxH,IAAM,SAAS,GAAG,UAAU,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC7F,IAAM,gBAAgB,GAAG,gDAA8C,SAAW,CAAA;IAClF,IAAM,eAAe,GAAG,CAAG,eAAQ,CAAC,QAAQ,CAAC,SAAM,CAAA,CAAC,MAAM,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,cAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAA;IAEvH,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAA;AACjE,CAAC;AAED;;GAEG;AACH,yBAA0B,aAAqB,EAAE,QAAgB;IAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IAC3C,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAA;IACzB,SAAS,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC9B,OAAO,SAAS,CAAC,UAAU,CAAA;IAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;AAClC,CAAC;AAED;;GAEG;AACH,sBAAuB,UAAkB,EAAE,QAAgB;IACzD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;SAC/B,MAAM,CAAC,cAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;SACjC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;SACtB,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;SAC1B,MAAM,CAAC,KAAK,CAAC,CAAA;AAClB,CAAC;AAED;;;GAGG;AACH,6BAA8B,QAAgB;IAC5C,OAAO,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACjD,CAAC;AAED;;GAEG;AACH,2BAA4B,GAAW;IACrC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AACtF,CAAC;AAED;;GAEG;AACH,2BAA4B,WAA6B,EAAE,MAAgB;IACzE,OAAO,WAAW,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAA7B,CAA6B,CAAC,CAAA;AAC/D,CAAC", "sourcesContent": ["import { relative, basename, extname, resolve, dirname, join } from 'path'\nimport { readFileSync, writeFileSync } from 'fs'\nimport { EOL, tmpdir, homedir } from 'os'\nimport sourceMapSupport = require('source-map-support')\nimport mkdirp = require('mkdirp')\nimport crypto = require('crypto')\nimport yn = require('yn')\nimport arrify = require('arrify')\nimport bufferFrom = require('buffer-from')\nimport { BaseError } from 'make-error'\nimport * as util from 'util'\nimport * as _ts from 'typescript'\n\n/**\n * @internal\n */\nexport const INSPECT_CUSTOM = util.inspect.custom || 'inspect'\n\n/**\n * Debugging `ts-node`.\n */\nconst shouldDebug = yn(process.env.TS_NODE_DEBUG)\nconst debug = shouldDebug ? console.log.bind(console, 'ts-node') : () => undefined\nconst debugFn = shouldDebug ?\n  <T, U> (key: string, fn: (arg: T) => U) => {\n    return (x: T) => {\n      debug(key, x)\n      return fn(x)\n    }\n  } :\n  <T, U> (_: string, fn: (arg: T) => U) => fn\n\n/**\n * Common TypeScript interfaces between versions.\n */\nexport interface TSCommon {\n  version: typeof _ts.version\n  sys: typeof _ts.sys\n  ScriptSnapshot: typeof _ts.ScriptSnapshot\n  displayPartsToString: typeof _ts.displayPartsToString\n  createLanguageService: typeof _ts.createLanguageService\n  getDefaultLibFilePath: typeof _ts.getDefaultLibFilePath\n  getPreEmitDiagnostics: typeof _ts.getPreEmitDiagnostics\n  flattenDiagnosticMessageText: typeof _ts.flattenDiagnosticMessageText\n  transpileModule: typeof _ts.transpileModule\n  ModuleKind: typeof _ts.ModuleKind\n  ScriptTarget: typeof _ts.ScriptTarget\n  findConfigFile: typeof _ts.findConfigFile\n  readConfigFile: typeof _ts.readConfigFile\n  parseJsonConfigFileContent: typeof _ts.parseJsonConfigFileContent\n  formatDiagnostics: typeof _ts.formatDiagnostics\n  formatDiagnosticsWithColorAndContext: typeof _ts.formatDiagnosticsWithColorAndContext\n}\n\n/**\n * Export the current version.\n */\nexport const VERSION = require('../package.json').version\n\n/**\n * Registration options.\n */\nexport interface Options {\n  pretty?: boolean | null\n  typeCheck?: boolean | null\n  transpileOnly?: boolean | null\n  files?: boolean | null\n  cache?: boolean | null\n  cacheDirectory?: string\n  compiler?: string\n  ignore?: string | string[]\n  project?: string\n  skipIgnore?: boolean | null\n  skipProject?: boolean | null\n  compilerOptions?: object\n  ignoreDiagnostics?: number | string | Array<number | string>\n  readFile?: (path: string) => string | undefined\n  fileExists?: (path: string) => boolean\n  transformers?: _ts.CustomTransformers\n}\n\n/**\n * Track the project information.\n */\ninterface MemoryCache {\n  contents: { [path: string]: string | undefined }\n  versions: { [path: string]: number | undefined }\n  outputs: { [path: string]: string }\n}\n\n/**\n * Information retrieved from type info check.\n */\nexport interface TypeInfo {\n  name: string\n  comment: string\n}\n\n/**\n * Default register options.\n */\nexport const DEFAULTS: Options = {\n  files: yn(process.env['TS_NODE_FILES']),\n  cache: yn(process.env['TS_NODE_CACHE'], { default: true }),\n  pretty: yn(process.env['TS_NODE_PRETTY']),\n  cacheDirectory: process.env['TS_NODE_CACHE_DIRECTORY'],\n  compiler: process.env['TS_NODE_COMPILER'],\n  compilerOptions: parse(process.env['TS_NODE_COMPILER_OPTIONS']),\n  ignore: split(process.env['TS_NODE_IGNORE']),\n  project: process.env['TS_NODE_PROJECT'],\n  skipIgnore: yn(process.env['TS_NODE_SKIP_IGNORE']),\n  skipProject: yn(process.env['TS_NODE_SKIP_PROJECT']),\n  ignoreDiagnostics: split(process.env['TS_NODE_IGNORE_DIAGNOSTICS']),\n  typeCheck: yn(process.env['TS_NODE_TYPE_CHECK']),\n  transpileOnly: yn(process.env['TS_NODE_TRANSPILE_ONLY'])\n}\n\n/**\n * Default TypeScript compiler options required by `ts-node`.\n */\nconst DEFAULT_COMPILER_OPTIONS = {\n  sourceMap: true,\n  inlineSourceMap: false,\n  inlineSources: true,\n  declaration: false,\n  noEmit: false,\n  outDir: '$$ts-node$$'\n}\n\n/**\n * Split a string array of values.\n */\nexport function split (value: string | undefined) {\n  return typeof value === 'string' ? value.split(/ *, */g) : undefined\n}\n\n/**\n * Parse a string as JSON.\n */\nexport function parse (value: string | undefined): object | undefined {\n  return typeof value === 'string' ? JSON.parse(value) : undefined\n}\n\n/**\n * Replace backslashes with forward slashes.\n */\nexport function normalizeSlashes (value: string): string {\n  return value.replace(/\\\\/g, '/')\n}\n\n/**\n * TypeScript diagnostics error.\n */\nexport class TSError extends BaseError {\n  name = 'TSError'\n\n  constructor (public diagnosticText: string, public diagnosticCodes: number[]) {\n    super(`⨯ Unable to compile TypeScript:\\n${diagnosticText}`)\n  }\n\n  /**\n   * @internal\n   */\n  [INSPECT_CUSTOM] () {\n    return this.diagnosticText\n  }\n}\n\n/**\n * Return type for registering `ts-node`.\n */\nexport interface Register {\n  cwd: string\n  extensions: string[]\n  cachedir: string\n  ts: TSCommon\n  compile (code: string, fileName: string, lineOffset?: number): string\n  getTypeInfo (code: string, fileName: string, position: number): TypeInfo\n}\n\n/**\n * Return a default temp directory based on home directory of user.\n */\nfunction getTmpDir (): string {\n  const hash = crypto.createHash('sha256').update(homedir(), 'utf8').digest('hex')\n\n  return join(tmpdir(), `ts-node-${hash}`)\n}\n\n/**\n * Register TypeScript compiler.\n */\nexport function register (opts: Options = {}): Register {\n  const options = Object.assign({}, DEFAULTS, opts)\n  const cacheDirectory = options.cacheDirectory || getTmpDir()\n  const originalJsHandler = require.extensions['.js']\n\n  const ignoreDiagnostics = arrify(options.ignoreDiagnostics).concat([\n    6059, // \"'rootDir' is expected to contain all source files.\"\n    18002, // \"The 'files' list in config file is empty.\"\n    18003 // \"No inputs were found in config file.\"\n  ]).map(Number)\n\n  const memoryCache: MemoryCache = {\n    contents: Object.create(null),\n    versions: Object.create(null),\n    outputs: Object.create(null)\n  }\n\n  const ignore = options.skipIgnore ? [] : arrify(\n    options.ignore || '/node_modules/'\n  ).map(str => new RegExp(str))\n\n  // Install source map support and read from memory cache.\n  sourceMapSupport.install({\n    environment: 'node',\n    retrieveFile (path: string) {\n      return memoryCache.outputs[path]\n    }\n  })\n\n  // Require the TypeScript compiler and configuration.\n  const cwd = process.cwd()\n  const { compilerOptions, project, skipProject } = options\n  const compiler = options.compiler || 'typescript'\n  const typeCheck = options.typeCheck === true || options.transpileOnly !== true\n  const ts: typeof _ts = require(compiler)\n  const transformers = options.transformers || undefined\n  const readFile = options.readFile || ts.sys.readFile\n  const fileExists = options.fileExists || ts.sys.fileExists\n  const config = readConfig(cwd, ts, fileExists, readFile, compilerOptions, project, skipProject)\n  const configDiagnosticList = filterDiagnostics(config.errors, ignoreDiagnostics)\n  const extensions = ['.ts', '.tsx']\n  const fileNames = options.files ? config.fileNames : []\n\n  const cachedir = join(\n    resolve(cwd, cacheDirectory),\n    getCompilerDigest({\n      version: ts.version,\n      options: config.options,\n      fileNames,\n      typeCheck,\n      ignoreDiagnostics,\n      compiler\n    })\n  )\n\n  const diagnosticHost: _ts.FormatDiagnosticsHost = {\n    getNewLine: () => EOL,\n    getCurrentDirectory: () => cwd,\n    getCanonicalFileName: (path) => path\n  }\n\n  const formatDiagnostics = options.pretty\n    ? ts.formatDiagnosticsWithColorAndContext\n    : ts.formatDiagnostics\n\n  function createTSError (diagnostics: ReadonlyArray<_ts.Diagnostic>) {\n    const diagnosticText = formatDiagnostics(diagnostics, diagnosticHost)\n    const diagnosticCodes = diagnostics.map(x => x.code)\n    return new TSError(diagnosticText, diagnosticCodes)\n  }\n\n  // Render the configuration errors and exit the script.\n  if (configDiagnosticList.length) throw createTSError(configDiagnosticList)\n\n  // Enable `allowJs` when flag is set.\n  if (config.options.allowJs) {\n    extensions.push('.js')\n    extensions.push('.jsx')\n  }\n\n  // Initialize files from TypeScript into project.\n  for (const path of fileNames) memoryCache.versions[path] = 1\n\n  /**\n   * Get the extension for a transpiled file.\n   */\n  const getExtension = config.options.jsx === ts.JsxEmit.Preserve ?\n    ((path: string) => /\\.[tj]sx$/.test(path) ? '.jsx' : '.js') :\n    ((_: string) => '.js')\n\n  /**\n   * Create the basic required function using transpile mode.\n   */\n  let getOutput = function (code: string, fileName: string, lineOffset = 0): SourceOutput {\n    const result = ts.transpileModule(code, {\n      fileName,\n      transformers,\n      compilerOptions: config.options,\n      reportDiagnostics: true\n    })\n\n    const diagnosticList = result.diagnostics ?\n      filterDiagnostics(result.diagnostics, ignoreDiagnostics) :\n      []\n\n    if (diagnosticList.length) throw createTSError(diagnosticList)\n\n    return [result.outputText, result.sourceMapText as string]\n  }\n\n  let getTypeInfo = function (_code: string, _fileName: string, _position: number): TypeInfo {\n    throw new TypeError(`Type information is unavailable without \"--type-check\"`)\n  }\n\n  // Use full language services when the fast option is disabled.\n  if (typeCheck) {\n    // Set the file contents into cache.\n    const updateMemoryCache = function (code: string, fileName: string) {\n      if (memoryCache.contents[fileName] !== code) {\n        memoryCache.contents[fileName] = code\n        memoryCache.versions[fileName] = (memoryCache.versions[fileName] || 0) + 1\n      }\n    }\n\n    // Create the compiler host for type checking.\n    const serviceHost = {\n      getScriptFileNames: () => Object.keys(memoryCache.versions),\n      getScriptVersion: (fileName: string) => {\n        const version = memoryCache.versions[fileName]\n\n        // We need to return `undefined` and not a string here because TypeScript will use\n        // `getScriptVersion` and compare against their own version - which can be `undefined`.\n        // If we don't return `undefined` it results in `undefined === \"undefined\"` and run\n        // `createProgram` again (which is very slow). Using a `string` assertion here to avoid\n        // TypeScript errors from the function signature (expects `(x: string) => string`).\n        return version === undefined ? undefined as any as string : String(version)\n      },\n      getScriptSnapshot (fileName: string) {\n        // Read contents into TypeScript memory cache.\n        if (!Object.prototype.hasOwnProperty.call(memoryCache.contents, fileName)) {\n          memoryCache.contents[fileName] = readFile(fileName)\n        }\n\n        const contents = memoryCache.contents[fileName]\n        if (contents === undefined) return\n        return ts.ScriptSnapshot.fromString(contents)\n      },\n      fileExists: debugFn('fileExists', fileExists),\n      readFile: debugFn('readFile', readFile),\n      readDirectory: debugFn('readDirectory', ts.sys.readDirectory),\n      getDirectories: debugFn('getDirectories', ts.sys.getDirectories),\n      directoryExists: debugFn('directoryExists', ts.sys.directoryExists),\n      getNewLine: () => EOL,\n      getCurrentDirectory: () => cwd,\n      getCompilationSettings: () => config.options,\n      getDefaultLibFileName: () => ts.getDefaultLibFilePath(config.options),\n      getCustomTransformers: () => transformers\n    }\n\n    const service = ts.createLanguageService(serviceHost)\n\n    getOutput = function (code: string, fileName: string, lineOffset: number = 0) {\n      // Must set memory cache before attempting to read file.\n      updateMemoryCache(code, fileName)\n\n      const output = service.getEmitOutput(fileName)\n\n      // Get the relevant diagnostics - this is 3x faster than `getPreEmitDiagnostics`.\n      const diagnostics = service.getCompilerOptionsDiagnostics()\n        .concat(service.getSyntacticDiagnostics(fileName))\n        .concat(service.getSemanticDiagnostics(fileName))\n\n      const diagnosticList = filterDiagnostics(diagnostics, ignoreDiagnostics)\n\n      if (diagnosticList.length) throw createTSError(diagnosticList)\n\n      if (output.emitSkipped) {\n        throw new TypeError(`${relative(cwd, fileName)}: Emit skipped`)\n      }\n\n      // Throw an error when requiring `.d.ts` files.\n      if (output.outputFiles.length === 0) {\n        throw new TypeError(\n          'Unable to require `.d.ts` file.\\n' +\n          'This is usually the result of a faulty configuration or import. ' +\n          'Make sure there is a `.js`, `.json` or another executable extension and ' +\n          'loader (attached before `ts-node`) available alongside ' +\n          `\\`${basename(fileName)}\\`.`\n        )\n      }\n\n      return [output.outputFiles[1].text, output.outputFiles[0].text]\n    }\n\n    getTypeInfo = function (code: string, fileName: string, position: number) {\n      updateMemoryCache(code, fileName)\n\n      const info = service.getQuickInfoAtPosition(fileName, position)\n      const name = ts.displayPartsToString(info ? info.displayParts : [])\n      const comment = ts.displayPartsToString(info ? info.documentation : [])\n\n      return { name, comment }\n    }\n  }\n\n  const compile = readThrough(cachedir, options.cache === true, memoryCache, getOutput, getExtension)\n  const register: Register = { cwd, compile, getTypeInfo, extensions, cachedir, ts }\n\n  // Register the extensions.\n  extensions.forEach(extension => {\n    registerExtension(extension, ignore, register, originalJsHandler)\n  })\n\n  return register\n}\n\n/**\n * Check if the filename should be ignored.\n */\nfunction shouldIgnore (filename: string, ignore: RegExp[]) {\n  const relname = normalizeSlashes(filename)\n\n  return ignore.some(x => x.test(relname))\n}\n\n/**\n * Register the extension for node.\n */\nfunction registerExtension (\n  ext: string,\n  ignore: RegExp[],\n  register: Register,\n  originalHandler: (m: NodeModule, filename: string) => any\n) {\n  const old = require.extensions[ext] || originalHandler\n\n  require.extensions[ext] = function (m: any, filename) {\n    if (shouldIgnore(filename, ignore)) {\n      return old(m, filename)\n    }\n\n    const _compile = m._compile\n\n    m._compile = function (code: string, fileName: string) {\n      debug('module._compile', fileName)\n\n      return _compile.call(this, register.compile(code, fileName), fileName)\n    }\n\n    return old(m, filename)\n  }\n}\n\n/**\n * Do post-processing on config options to support `ts-node`.\n */\nfunction fixConfig (ts: TSCommon, config: _ts.ParsedCommandLine) {\n  // Delete options that *should not* be passed through.\n  delete config.options.out\n  delete config.options.outFile\n  delete config.options.composite\n  delete config.options.declarationDir\n  delete config.options.declarationMap\n  delete config.options.emitDeclarationOnly\n\n  // Target ES5 output by default (instead of ES3).\n  if (config.options.target === undefined) {\n    config.options.target = ts.ScriptTarget.ES5\n  }\n\n  // Target CommonJS modules by default (instead of magically switching to ES6 when the target is ES6).\n  if (config.options.module === undefined) {\n    config.options.module = ts.ModuleKind.CommonJS\n  }\n\n  return config\n}\n\n/**\n * Load TypeScript configuration.\n */\nfunction readConfig (\n  cwd: string,\n  ts: TSCommon,\n  fileExists: (path: string) => boolean,\n  readFile: (path: string) => string | undefined,\n  compilerOptions?: object,\n  project?: string | null,\n  noProject?: boolean | null\n): _ts.ParsedCommandLine {\n  let config = { compilerOptions: {} }\n  let basePath = normalizeSlashes(cwd)\n  let configFileName: string | undefined = undefined\n\n  // Read project configuration when available.\n  if (!noProject) {\n    configFileName = project\n      ? normalizeSlashes(resolve(cwd, project))\n      : ts.findConfigFile(normalizeSlashes(cwd), fileExists)\n\n    if (configFileName) {\n      const result = ts.readConfigFile(configFileName, readFile)\n\n      // Return diagnostics.\n      if (result.error) {\n        return { errors: [result.error], fileNames: [], options: {} }\n      }\n\n      config = result.config\n      basePath = normalizeSlashes(dirname(configFileName))\n    }\n  }\n\n  // Override default configuration options `ts-node` requires.\n  config.compilerOptions = Object.assign({}, config.compilerOptions, compilerOptions, DEFAULT_COMPILER_OPTIONS)\n\n  return fixConfig(ts, ts.parseJsonConfigFileContent(config, ts.sys, basePath, undefined, configFileName))\n}\n\n/**\n * Internal source output.\n */\ntype SourceOutput = [string, string]\n\n/**\n * Wrap the function with caching.\n */\nfunction readThrough (\n  cachedir: string,\n  shouldCache: boolean,\n  memoryCache: MemoryCache,\n  compile: (code: string, fileName: string, lineOffset?: number) => SourceOutput,\n  getExtension: (fileName: string) => string\n) {\n  if (shouldCache === false) {\n    return function (code: string, fileName: string, lineOffset?: number) {\n      debug('readThrough', fileName)\n\n      const [value, sourceMap] = compile(code, fileName, lineOffset)\n      const output = updateOutput(value, fileName, sourceMap, getExtension)\n\n      memoryCache.outputs[fileName] = output\n\n      return output\n    }\n  }\n\n  // Make sure the cache directory exists before continuing.\n  mkdirp.sync(cachedir)\n\n  return function (code: string, fileName: string, lineOffset?: number) {\n    debug('readThrough', fileName)\n\n    const cachePath = join(cachedir, getCacheName(code, fileName))\n    const extension = getExtension(fileName)\n    const outputPath = `${cachePath}${extension}`\n\n    try {\n      const output = readFileSync(outputPath, 'utf8')\n      if (isValidCacheContent(output)) {\n        memoryCache.outputs[fileName] = output\n        return output\n      }\n    } catch (err) {/* Ignore. */}\n\n    const [value, sourceMap] = compile(code, fileName, lineOffset)\n    const output = updateOutput(value, fileName, sourceMap, getExtension)\n\n    memoryCache.outputs[fileName] = output\n    writeFileSync(outputPath, output)\n\n    return output\n  }\n}\n\n/**\n * Update the output remapping the source map.\n */\nfunction updateOutput (outputText: string, fileName: string, sourceMap: string, getExtension: (fileName: string) => string) {\n  const base64Map = bufferFrom(updateSourceMap(sourceMap, fileName), 'utf8').toString('base64')\n  const sourceMapContent = `data:application/json;charset=utf-8;base64,${base64Map}`\n  const sourceMapLength = `${basename(fileName)}.map`.length + (getExtension(fileName).length - extname(fileName).length)\n\n  return outputText.slice(0, -sourceMapLength) + sourceMapContent\n}\n\n/**\n * Update the source map contents for improved output.\n */\nfunction updateSourceMap (sourceMapText: string, fileName: string) {\n  const sourceMap = JSON.parse(sourceMapText)\n  sourceMap.file = fileName\n  sourceMap.sources = [fileName]\n  delete sourceMap.sourceRoot\n  return JSON.stringify(sourceMap)\n}\n\n/**\n * Get the file name for the cache entry.\n */\nfunction getCacheName (sourceCode: string, fileName: string) {\n  return crypto.createHash('sha256')\n    .update(extname(fileName), 'utf8')\n    .update('\\x00', 'utf8')\n    .update(sourceCode, 'utf8')\n    .digest('hex')\n}\n\n/**\n * Ensure the given cached content is valid by sniffing for a base64 encoded '}'\n * at the end of the content, which should exist if there is a valid sourceMap present.\n */\nfunction isValidCacheContent (contents: string) {\n  return /(?:9|0=|Q==)$/.test(contents.slice(-3))\n}\n\n/**\n * Create a hash of the current configuration.\n */\nfunction getCompilerDigest (obj: object) {\n  return crypto.createHash('sha256').update(JSON.stringify(obj), 'utf8').digest('hex')\n}\n\n/**\n * Filter diagnostics.\n */\nfunction filterDiagnostics (diagnostics: _ts.Diagnostic[], ignore: number[]) {\n  return diagnostics.filter(x => ignore.indexOf(x.code) === -1)\n}\n"]}