# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


abbrev@1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"

abbrev@1.0.x:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.0.9.tgz#91b4792588a7738c25f35dd6f63752a2f8776135"

accepts@1.3.3:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.3.3.tgz#c3ca7434938648c3e0d9c1e328dd68b622c284ca"
  dependencies:
    mime-types "~2.1.11"
    negotiator "0.6.1"

accepts@~1.3.4:
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.3.5.tgz#eb777df6011723a3b14e8a72c0805c8e86746bd2"
  dependencies:
    mime-types "~2.1.18"
    negotiator "0.6.1"

acorn@^3.0.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"

adm-zip@~0.4.3:
  version "0.4.7"
  resolved "https://registry.yarnpkg.com/adm-zip/-/adm-zip-0.4.7.tgz#8606c2cbf1c426ce8c8ec00174447fd49b6eafc1"

after@0.8.2:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/after/-/after-0.8.2.tgz#fedb394f9f0e02aa9768e702bda23b505fae7e1f"

agent-base@2:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/agent-base/-/agent-base-2.1.1.tgz#d6de10d5af6132d5bd692427d46fc538539094c7"
  dependencies:
    extend "~3.0.0"
    semver "~5.0.1"

ajv@^4.9.1:
  version "4.11.8"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-4.11.8.tgz#82ffb02b29e662ae53bdc20af15947706739c536"
  dependencies:
    co "^4.6.0"
    json-stable-stringify "^1.0.1"

ajv@^5.1.0:
  version "5.5.2"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-5.5.2.tgz#73b5eeca3fab653e3d3f9422b341ad42205dc965"
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-escapes@^1.1.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  dependencies:
    color-convert "^1.9.0"

anymatch@^1.3.0:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/anymatch/-/anymatch-1.3.2.tgz#553dcb8f91e3c889845dfdba34c77721b90b9d7a"
  dependencies:
    micromatch "^2.1.5"
    normalize-path "^2.0.0"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/append-transform/-/append-transform-0.4.0.tgz#d76ebf8ca94d276e247a36bad44a4b74ab611991"
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.0.3:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/aproba/-/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"

archiver@~0.14.0:
  version "0.14.4"
  resolved "https://registry.yarnpkg.com/archiver/-/archiver-0.14.4.tgz#5b9ddb9f5ee1ceef21cb8f3b020e6240ecb4315c"
  dependencies:
    async "~0.9.0"
    buffer-crc32 "~0.2.1"
    glob "~4.3.0"
    lazystream "~0.1.0"
    lodash "~3.2.0"
    readable-stream "~1.0.26"
    tar-stream "~1.1.0"
    zip-stream "~0.5.0"

archy@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/archy/-/archy-1.0.0.tgz#f9c8c13757cc1dd7bc379ac77b2c62a5c2868c40"

are-we-there-yet@~1.1.2:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/are-we-there-yet/-/are-we-there-yet-1.1.4.tgz#bb5dca382bb94f05e15194373d16fd3ba1ca110d"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.2, argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  dependencies:
    sprintf-js "~1.0.2"

"argparse@~ 0.1.11":
  version "0.1.16"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-0.1.16.tgz#cfd01e0fbba3d6caed049fbd758d40f65196f57c"
  dependencies:
    underscore "~1.7.0"
    underscore.string "~2.4.0"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-flatten@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-slice@^0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/array-slice/-/array-slice-0.2.3.tgz#dd3cfb80ed7973a75117cdac69b0b99ec86186f5"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

arraybuffer.slice@0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/arraybuffer.slice/-/arraybuffer.slice-0.0.6.tgz#f33b2159f0532a3f3107a272c0ccfbd1ad2979ca"

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asn1@0.1.11:
  version "0.1.11"
  resolved "https://registry.yarnpkg.com/asn1/-/asn1-0.1.11.tgz#559be18376d08a4ec4dbe80877d27818639b2df7"

asn1@~0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/asn1/-/asn1-0.2.3.tgz#dac8787713c9966849fc8180777ebe9c1ddf3b86"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assert-plus@^0.1.5:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-0.1.5.tgz#ee74009413002d84cec7219c6ac811812e723160"

assert-plus@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-0.2.0.tgz#d74e1b87e7affc0db8aadb7021f3fe48101ab234"

assert@^1.1.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/assert/-/assert-1.4.1.tgz#99912d591836b5a6f5b345c0f07eefc08fc65d91"
  dependencies:
    util "0.10.3"

assertion-error@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/assertion-error/-/assertion-error-1.1.0.tgz#e60b6b0e8f301bd97e5375215bda406c85118c0b"

async-each@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/async-each/-/async-each-1.0.1.tgz#19d386a1d9edc6e7c1c85d388aedbcc56d33602d"

async@0.1.x, async@~0.1.18, async@~0.1.22:
  version "0.1.22"
  resolved "https://registry.yarnpkg.com/async/-/async-0.1.22.tgz#0fc1aaa088a0e3ef0ebe2d8831bab0dcf8845061"

async@1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/async/-/async-1.4.0.tgz#35f86f83c59e0421d099cd9a91d8278fb578c00d"

async@1.x, async@^1.3.0, async@^1.4.0, async@^1.4.2, async@^1.5.0, async@^1.5.2:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

async@^0.9.0, async@~0.9.0:
  version "0.9.2"
  resolved "https://registry.yarnpkg.com/async/-/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"

async@~0.2.6:
  version "0.2.10"
  resolved "https://registry.yarnpkg.com/async/-/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"

async@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/async/-/async-1.0.0.tgz#f8fc04ca3a13784ade9e1641af98578cfbd647a9"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

aws-sign2@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.5.0.tgz#c57103f7a17fc037f02d7c2e64b602ea223f7d63"

aws-sign2@~0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.6.0.tgz#14342dd38dbcc94d0e5b87d763cd63612c0e794f"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"

aws4@^1.2.1, aws4@^1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/aws4/-/aws4-1.6.0.tgz#83ef5ca860b2b32e4a0deedee8c771b9db57471e"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.0.0, babel-core@^6.0.12, babel-core@^6.26.0, babel-core@^6.6.5:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-core/-/babel-core-6.26.0.tgz#af32f78b31a6fcef119c87b0fd8d9753f03a0bb8"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.0"
    debug "^2.6.8"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.7"
    slash "^1.0.0"
    source-map "^0.5.6"

babel-generator@^6.18.0, babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://registry.yarnpkg.com/babel-generator/-/babel-generator-6.26.1.tgz#1844408d3b8f0d35a404ea7ac180f087a601bd90"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz#a5f56dab41a25f97ecb498c7ebaca9819f95be5f"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz#325c59f902f82f24b74faceed0363954f6495e72"
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helpers/-/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-loader@^6.0.0:
  version "6.4.1"
  resolved "https://registry.yarnpkg.com/babel-loader/-/babel-loader-6.4.1.tgz#0b34112d5b0748a8dcdbf51acf6f9bd42d50b8ca"
  dependencies:
    find-cache-dir "^0.1.1"
    loader-utils "^0.2.16"
    mkdirp "^0.5.1"
    object-assign "^4.0.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-messages/-/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.3.13:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-syntax-async-functions@^6.3.13:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"

babel-plugin-transform-es2015-arrow-functions@^6.3.13:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.3.13:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.6.0, babel-plugin-transform-es2015-block-scoping@^6.6.5:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz#d70f5299c1308d05c12f463813b0a09e73b1895f"
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.6.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.6.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.6.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.6.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.3.13:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-commonjs@6.7.7:
  version "6.7.7"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.7.7.tgz#fa5ca2016617c4d712123d8cfc15787fcaa83f33"
  dependencies:
    babel-plugin-transform-strict-mode "^6.6.5"
    babel-runtime "^5.0.0"
    babel-template "^6.7.0"
    babel-types "^6.7.7"

babel-plugin-transform-es2015-modules-commonjs@^6.6.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.0.tgz#0d8394029b7dc6abe1a97ef181e00758dd2e5d8a"
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-object-super@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.6.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.3.13:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.6.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.6.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.3.13:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-es3-member-expression-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es3-member-expression-literals/-/babel-plugin-transform-es3-member-expression-literals-6.22.0.tgz#733d3444f3ecc41bef8ed1a6a4e09657b8969ebb"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es3-property-literals@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es3-property-literals/-/babel-plugin-transform-es3-property-literals-6.22.0.tgz#b2078d5842e22abf40f73e8cde9cd3711abd5758"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@6.6.5:
  version "6.6.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.6.5.tgz#079a982bd56e2235e31ee3b17ad54aeba898d4e7"
  dependencies:
    babel-core "^6.6.5"
    babel-plugin-syntax-async-functions "^6.3.13"
    babel-plugin-transform-es2015-block-scoping "^6.6.5"
    babel-plugin-transform-es2015-for-of "^6.6.0"
    babel-runtime "^5.0.0"
    babel-traverse "^6.6.5"
    babel-types "^6.6.5"
    babylon "^6.6.5"
    private "~0.1.5"

babel-plugin-transform-regenerator@^6.6.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz#e0703696fbde27f0a3efcacf8b4dca2f7b3a8f2f"
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-strict-mode@^6.24.1, babel-plugin-transform-strict-mode@^6.6.5:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-preset-es2015-mod@^6.3.13:
  version "6.6.0"
  resolved "https://registry.yarnpkg.com/babel-preset-es2015-mod/-/babel-preset-es2015-mod-6.6.0.tgz#e105b62eb7c1001090ab86225298904cf90c1e8e"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "6.7.7"
    babel-plugin-transform-regenerator "6.6.5"
    babel-preset-es2015 "6.6.0"
    modify-babel-preset "2.0.2"

babel-preset-es2015@6.6.0:
  version "6.6.0"
  resolved "https://registry.yarnpkg.com/babel-preset-es2015/-/babel-preset-es2015-6.6.0.tgz#88b33e58fec94c6ebde58dc65ece5d14e0ec2568"
  dependencies:
    babel-plugin-check-es2015-constants "^6.3.13"
    babel-plugin-transform-es2015-arrow-functions "^6.3.13"
    babel-plugin-transform-es2015-block-scoped-functions "^6.3.13"
    babel-plugin-transform-es2015-block-scoping "^6.6.0"
    babel-plugin-transform-es2015-classes "^6.6.0"
    babel-plugin-transform-es2015-computed-properties "^6.3.13"
    babel-plugin-transform-es2015-destructuring "^6.6.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.6.0"
    babel-plugin-transform-es2015-for-of "^6.6.0"
    babel-plugin-transform-es2015-function-name "^6.3.13"
    babel-plugin-transform-es2015-literals "^6.3.13"
    babel-plugin-transform-es2015-modules-commonjs "^6.6.0"
    babel-plugin-transform-es2015-object-super "^6.3.13"
    babel-plugin-transform-es2015-parameters "^6.6.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.3.13"
    babel-plugin-transform-es2015-spread "^6.3.13"
    babel-plugin-transform-es2015-sticky-regex "^6.3.13"
    babel-plugin-transform-es2015-template-literals "^6.6.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.6.0"
    babel-plugin-transform-es2015-unicode-regex "^6.3.13"
    babel-plugin-transform-regenerator "^6.6.0"

babel-preset-es3@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/babel-preset-es3/-/babel-preset-es3-1.0.1.tgz#e08dd950a1670dab8b50abceaa9b93d3d9accd1e"
  dependencies:
    babel-plugin-transform-es3-member-expression-literals "^6.8.0"
    babel-plugin-transform-es3-property-literals "^6.8.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-register/-/babel-register-6.26.0.tgz#6ed021173e2fcb486d7acb45c6009a856f647071"
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@^5.0.0:
  version "5.8.38"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-5.8.38.tgz#1c0b02eb63312f5f087ff20450827b425c9d4c19"
  dependencies:
    core-js "^1.0.0"

babel-runtime@^6.18.0, babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.16.0, babel-template@^6.24.1, babel-template@^6.26.0, babel-template@^6.7.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-template/-/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.18.0, babel-traverse@^6.24.1, babel-traverse@^6.26.0, babel-traverse@^6.6.5:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-traverse/-/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.18.0, babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.26.0, babel-types@^6.6.5, babel-types@^6.7.7:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-types/-/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0, babylon@^6.6.5:
  version "6.18.0"
  resolved "https://registry.yarnpkg.com/babylon/-/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"

backo2@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/backo2/-/backo2-1.0.2.tgz#31ab1ac8b129363463e35b3ebb69f4dfcfba7947"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-arraybuffer@0.1.5:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz#73926771923b5a19747ad666aa5cd4bf9c6e9ce8"

base64-js@^1.0.2:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.2.3.tgz#fb13668233d9614cf5fb4bce95a9ba4096cdf801"

base64id@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/base64id/-/base64id-1.0.0.tgz#47688cb99bb6804f0e06d3e763b1c32e57d8e6b6"

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/batch/-/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"

batch@^0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/batch/-/batch-0.5.3.tgz#3f3414f380321743bfc1042f9a83ff1d5824d464"

bcrypt-pbkdf@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz#63bc5dcb61331b92bc05fd528953c33462a06f8d"
  dependencies:
    tweetnacl "^0.14.3"

better-assert@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/better-assert/-/better-assert-1.0.2.tgz#40866b9e1b9e0b55b481894311e68faffaebc522"
  dependencies:
    callsite "1.0.0"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/big.js/-/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"

binary-extensions@^1.0.0:
  version "1.11.0"
  resolved "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-1.11.0.tgz#46aa1751fb6a2f93ee5e689bb1087d4b14c6c205"

bind-obj-methods@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/bind-obj-methods/-/bind-obj-methods-1.0.0.tgz#4f5979cac15793adf70e488161e463e209ca509c"

bl@^0.9.0, bl@~0.9.0:
  version "0.9.5"
  resolved "https://registry.yarnpkg.com/bl/-/bl-0.9.5.tgz#c06b797af085ea00bc527afc8efcf11de2232054"
  dependencies:
    readable-stream "~1.0.26"

blob@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/blob/-/blob-0.0.4.tgz#bcf13052ca54463f30f9fc7e95b9a47630a94921"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.yarnpkg.com/block-stream/-/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bluebird@^2.9.27, bluebird@^2.9.30:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/bluebird/-/bluebird-2.11.0.tgz#534b9033c022c9579c56ba3b3e5a5caafbb650e1"

bluebird@^3.5.1:
  version "3.5.1"
  resolved "https://registry.yarnpkg.com/bluebird/-/bluebird-3.5.1.tgz#d9551f9de98f1fcda1e683d17ee91a0602ee2eb9"

body-parser@1.18.2, body-parser@^1.12.4:
  version "1.18.2"
  resolved "https://registry.yarnpkg.com/body-parser/-/body-parser-1.18.2.tgz#87678a19d84b47d859b83199bd59bce222b10454"
  dependencies:
    bytes "3.0.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.1"
    http-errors "~1.6.2"
    iconv-lite "0.4.19"
    on-finished "~2.3.0"
    qs "6.5.1"
    raw-body "2.3.2"
    type-is "~1.6.15"

body-parser@~1.14.0:
  version "1.14.2"
  resolved "https://registry.yarnpkg.com/body-parser/-/body-parser-1.14.2.tgz#1015cb1fe2c443858259581db53332f8d0cf50f9"
  dependencies:
    bytes "2.2.0"
    content-type "~1.0.1"
    debug "~2.2.0"
    depd "~1.1.0"
    http-errors "~1.3.1"
    iconv-lite "0.4.13"
    on-finished "~2.3.0"
    qs "5.2.0"
    raw-body "~2.1.5"
    type-is "~1.6.10"

boom@2.x.x:
  version "2.10.1"
  resolved "https://registry.yarnpkg.com/boom/-/boom-2.10.1.tgz#39c8918ceff5799f83f9492a848f625add0c766f"
  dependencies:
    hoek "2.x.x"

boom@4.x.x:
  version "4.3.1"
  resolved "https://registry.yarnpkg.com/boom/-/boom-4.3.1.tgz#4f8a3005cb4a7e3889f749030fd25b96e01d2e31"
  dependencies:
    hoek "4.x.x"

boom@5.x.x:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/boom/-/boom-5.2.0.tgz#5dd9da6ee3a5f302077436290cb717d3f4a54e02"
  dependencies:
    hoek "4.x.x"

brace-expansion@^1.0.0, brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^0.1.2:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/braces/-/braces-0.1.5.tgz#c085711085291d8b75fdd74eab0f8597280711e6"
  dependencies:
    expand-range "^0.1.0"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://registry.yarnpkg.com/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

browserify-aes@0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/browserify-aes/-/browserify-aes-0.4.0.tgz#067149b668df31c4b58533e02d01e806d8608e2c"
  dependencies:
    inherits "^2.0.1"

browserify-zlib@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/browserify-zlib/-/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  dependencies:
    pako "~0.2.0"

buffer-crc32@~0.2.1:
  version "0.2.13"
  resolved "https://registry.yarnpkg.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"

buffer@^4.9.0:
  version "4.9.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-modules@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/builtin-modules/-/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"

bytes@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-0.1.0.tgz#c574812228126d6369d1576925a8579db3f8e5a2"

bytes@2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-2.2.0.tgz#fd35464a403f6f9117c2de3609ecff9cae000588"

bytes@2.4.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-2.4.0.tgz#7d97196f9d5baf7f6935e25985549edd2a6c2339"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"

caching-transform@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/caching-transform/-/caching-transform-1.0.1.tgz#6dbdb2f20f8d8fbce79f3e94e9d1742dcdf5c0a1"
  dependencies:
    md5-hex "^1.2.0"
    mkdirp "^0.5.1"
    write-file-atomic "^1.1.4"

callsite@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/callsite/-/callsite-1.0.0.tgz#280398e5d664bd74038b6f0905153e6e8af1bc20"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/camelcase-keys/-/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"

caseless@~0.11.0:
  version "0.11.0"
  resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.11.0.tgz#715b96ea9841593cc33067923f5ec60ebda4f7d7"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

caseless@~0.9.0:
  version "0.9.0"
  resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.9.0.tgz#b7b65ce6bf1413886539cfd533f0b30effa9cf88"

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chai@^3.3.0:
  version "3.5.0"
  resolved "https://registry.yarnpkg.com/chai/-/chai-3.5.0.tgz#4d02637b067fe958bdbfdd3a40ec56fef7373247"
  dependencies:
    assertion-error "^1.0.1"
    deep-eql "^0.1.3"
    type-detect "^1.0.0"

chalk@^1.0.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.1, chalk@^2.1.0:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.3.2.tgz#250dc96b07491bfd601e648d66ddf5f60c7a5c65"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chokidar@^1.0.0, chokidar@^1.4.1:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/chokidar/-/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

circular-json@^0.3.1:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/circular-json/-/circular-json-0.3.3.tgz#815c99ea84f6809529d2f45791bdf82711352d66"

clean-yaml-object@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/clean-yaml-object/-/clean-yaml-object-0.1.0.tgz#63fb110dc2ce1a84dc21f6d9334876d010ae8b68"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  dependencies:
    restore-cursor "^1.0.1"

cli-width@^1.0.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-1.1.1.tgz#a4d293ef67ebb7b88d4a4d42c0ccf00c4d1e366d"

cli@0.4.3:
  version "0.4.3"
  resolved "https://registry.yarnpkg.com/cli/-/cli-0.4.3.tgz#e6819c8d5faa957f64f98f66a8506268c1d1f17d"
  dependencies:
    glob ">= 3.1.4"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-4.0.0.tgz#743d4650e05f36d1ed2575b59638d87322bfbbcc"
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

clone@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/clone/-/clone-1.0.3.tgz#298d7e2231660f40c003c2ed3140decf3f53085f"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

coffee-script@~1.3.3:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/coffee-script/-/coffee-script-1.3.3.tgz#150d6b4cb522894369efed6a2101c20bc7f4a4f4"

color-convert@^1.9.0:
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.1.tgz#c1261107aeb2f294ebffec9ed9ecad529a6097ed"
  dependencies:
    color-name "^1.1.1"

color-name@^1.1.1:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

color-support@^1.1.0:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/color-support/-/color-support-1.1.3.tgz#93834379a1cc9a0c61f82f52f0d04322251bd5a2"

colors@0.x.x, colors@~0.6.0, colors@~0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/colors/-/colors-0.6.2.tgz#2423fe6678ac0c5dae8852e5d0e5be08c997abcc"

colors@^1.1.0, colors@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/colors/-/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"

combined-stream@1.0.6, combined-stream@^1.0.5, combined-stream@~1.0.5:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.6.tgz#723e7df6e801ac5613113a7e445a9b69cb632818"
  dependencies:
    delayed-stream "~1.0.0"

combined-stream@~0.0.4, combined-stream@~0.0.5:
  version "0.0.7"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-0.0.7.tgz#0137e657baa5a7541c57ac37ac5fc07d73b4dc1f"
  dependencies:
    delayed-stream "0.0.5"

commander@0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/commander/-/commander-0.6.1.tgz#fa68a14f6a945d54dbbe50d8cdb3320e9e3b1a06"

commander@2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.3.0.tgz#fd430e889832ec353b9acd1de217c11cb3eef873"

commander@^2.8.1, commander@^2.9.0:
  version "2.14.1"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.14.1.tgz#2235123e37af8ca3c65df45b026dbd357b01b9aa"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

component-bind@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/component-bind/-/component-bind-1.0.0.tgz#00c608ab7dcd93897c0009651b1d3a8e1e73bbd1"

component-emitter@1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.1.2.tgz#296594f2753daa63996d2af08d15a95116c9aec3"

component-emitter@1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"

component-inherit@0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/component-inherit/-/component-inherit-0.0.3.tgz#645fc4adf58b72b649d5cae65135619db26ff143"

compress-commons@~0.2.0:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/compress-commons/-/compress-commons-0.2.9.tgz#422d927430c01abd06cd455b6dfc04cb4cf8003c"
  dependencies:
    buffer-crc32 "~0.2.1"
    crc32-stream "~0.3.1"
    node-int64 "~0.3.0"
    readable-stream "~1.0.26"

compressible@~2.0.13:
  version "2.0.13"
  resolved "https://registry.yarnpkg.com/compressible/-/compressible-2.0.13.tgz#0d1020ab924b2fdb4d6279875c7d6daba6baa7a9"
  dependencies:
    mime-db ">= 1.33.0 < 2"

compression@^1.5.2:
  version "1.7.2"
  resolved "https://registry.yarnpkg.com/compression/-/compression-1.7.2.tgz#aaffbcd6aaf854b44ebb280353d5ad1651f59a69"
  dependencies:
    accepts "~1.3.4"
    bytes "3.0.0"
    compressible "~2.0.13"
    debug "2.6.9"
    on-headers "~1.0.1"
    safe-buffer "5.1.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.0.tgz#0aac662fd52be78964d5532f694784e70110acf7"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

concat-stream@^1.4.1, concat-stream@^1.4.6:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.1.tgz#261b8f518301f1d834e36342b9fea095d2620a26"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.3.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/connect-history-api-fallback/-/connect-history-api-fallback-1.5.0.tgz#b06873934bc5e344fef611a196a6faae0aee015a"

connect@^3.3.5:
  version "3.6.6"
  resolved "https://registry.yarnpkg.com/connect/-/connect-3.6.6.tgz#09eff6c55af7236e137135a72574858b6786f524"
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.0"
    parseurl "~1.3.2"
    utils-merge "1.0.1"

connect@~2.4.4:
  version "2.4.6"
  resolved "https://registry.yarnpkg.com/connect/-/connect-2.4.6.tgz#012c2fe05018504ed2028668a16903199e6e7ace"
  dependencies:
    bytes "0.1.0"
    cookie "0.0.4"
    crc "0.2.0"
    debug "*"
    formidable "1.0.11"
    fresh "0.1.0"
    pause "0.0.1"
    qs "0.5.1"
    send "0.0.4"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"

content-disposition@0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/content-disposition/-/content-disposition-0.5.2.tgz#0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4"

content-type@~1.0.1, content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"

convert-source-map@^1.1.1, convert-source-map@^1.3.0, convert-source-map@^1.5.0:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.5.1.tgz#b8278097b9bc229365de5c62cf5fcaed8b5599e5"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.0.4.tgz#5456bd47aee2666eac976ea80a6105940483fe98"

cookie@0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"

core-js@^2.1.0, core-js@^2.4.0, core-js@^2.5.0:
  version "2.5.3"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.5.3.tgz#8acc38345824f16d8365b7c9b4259168e8ed603e"

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

coveralls@^2.13.3:
  version "2.13.3"
  resolved "https://registry.yarnpkg.com/coveralls/-/coveralls-2.13.3.tgz#9ad7c2ae527417f361e8b626483f48ee92dd2bc7"
  dependencies:
    js-yaml "3.6.1"
    lcov-parse "0.0.10"
    log-driver "1.2.5"
    minimist "1.2.0"
    request "2.79.0"

crc32-stream@~0.3.1:
  version "0.3.4"
  resolved "https://registry.yarnpkg.com/crc32-stream/-/crc32-stream-0.3.4.tgz#73bc25b45fac1db6632231a7bfce8927e9f06552"
  dependencies:
    buffer-crc32 "~0.2.1"
    readable-stream "~1.0.24"

"crc32@>= 0.2.2":
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/crc32/-/crc32-0.2.2.tgz#7ad220d6ffdcd119f9fc127a7772cacea390a4ba"

crc@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/crc/-/crc-0.2.0.tgz#f4486b9bf0a12df83c3fca14e31e030fdabd9454"

cross-spawn@^4:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-4.0.2.tgz#7b9247621c23adfdd3856004a823cbe397424d41"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cryptiles@2.x.x:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/cryptiles/-/cryptiles-2.0.5.tgz#3bdfecdc608147c1c67202fa291e7dca59eaa3b8"
  dependencies:
    boom "2.x.x"

cryptiles@3.x.x:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/cryptiles/-/cryptiles-3.1.2.tgz#a89fbb220f5ce25ec56e8c4aa8a4fd7b5b0d29fe"
  dependencies:
    boom "5.x.x"

crypto-browserify@3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-3.3.0.tgz#b9fc75bb4a0ed61dcf1cd5dae96eb30c9c3e506c"
  dependencies:
    browserify-aes "0.4.0"
    pbkdf2-compat "2.0.1"
    ripemd160 "0.2.0"
    sha.js "2.2.6"

ctype@0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/ctype/-/ctype-0.5.3.tgz#82c18c2461f74114ef16c135224ad0b9144ca12f"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/currently-unhandled/-/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

custom-event@~1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/custom-event/-/custom-event-1.0.1.tgz#5d02a46850adf1b4a317946a3928fccb5bfd0425"

d@1:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/d/-/d-1.0.0.tgz#754bb5bfe55451da69a58b94d45f4c5b0462d58f"
  dependencies:
    es5-ext "^0.10.9"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/date-now/-/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

dateformat@1.0.2-1.2.3:
  version "1.0.2-1.2.3"
  resolved "https://registry.yarnpkg.com/dateformat/-/dateformat-1.0.2-1.2.3.tgz#b0220c02de98617433b72851cf47de3df2cdbee9"

debug-log@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/debug-log/-/debug-log-1.0.1.tgz#2307632d4c04382b8df8a32f70b895046d52745f"

debug@*, debug@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  dependencies:
    ms "2.0.0"

debug@2, debug@2.6.9, debug@^2.1.1, debug@^2.1.3, debug@^2.2.0, debug@^2.6.6, debug@^2.6.8:
  version "2.6.9"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  dependencies:
    ms "2.0.0"

debug@2.2.0, debug@~2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.2.0.tgz#f87057e995b1a1f6ae6a4960664137bc56f039da"
  dependencies:
    ms "0.7.1"

debug@2.3.3:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.3.3.tgz#40c453e67e6e13c901ddec317af8986cda9eff8c"
  dependencies:
    ms "0.7.2"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

deep-eql@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/deep-eql/-/deep-eql-0.1.3.tgz#ef558acab8de25206cd713906d74e56930eb69f2"
  dependencies:
    type-detect "0.1.1"

deep-extend@~0.4.0:
  version "0.4.2"
  resolved "https://registry.yarnpkg.com/deep-extend/-/deep-extend-0.4.2.tgz#48b699c27e334bf89f10892be432f6e4c7d34a7f"

deep-is@~0.1.2, deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/default-require-extensions/-/default-require-extensions-1.0.0.tgz#f37ea15d3e13ffd9b437d33e1a75b5fb97874cb8"
  dependencies:
    strip-bom "^2.0.0"

"deflate-js@>= 0.2.2":
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/deflate-js/-/deflate-js-0.2.3.tgz#f85abb58ebc5151a306147473d57c3e4f7e4426b"

del@^2.0.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/del/-/del-2.2.2.tgz#c12c981d067846c84bcaf862cff930d907ffd1a8"
  dependencies:
    globby "^5.0.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    rimraf "^2.2.8"

delayed-stream@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-0.0.5.tgz#d4b1f43a93e8296dfe02694f4680bc37a313c73f"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

depd@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/depd/-/depd-1.1.1.tgz#5783b4e1c459f06fa5ca27f991f3d06e7a310359"

depd@~1.1.0, depd@~1.1.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

detect-libc@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/detect-libc/-/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"

di@^0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/di/-/di-0.0.1.tgz#806649326ceaa7caa3306d75d985ea2748ba913c"

diff@1.4.0, diff@^1.3.2:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/diff/-/diff-1.4.0.tgz#7f28d2eb9ee7b15a97efd89ce63dcfdaa3ccbabf"

doctrine@^0.7.1:
  version "0.7.2"
  resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-0.7.2.tgz#7cb860359ba3be90e040b26b729ce4bfa654c523"
  dependencies:
    esutils "^1.1.6"
    isarray "0.0.1"

dom-serialize@^2.2.0:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/dom-serialize/-/dom-serialize-2.2.1.tgz#562ae8999f44be5ea3076f5419dcd59eb43ac95b"
  dependencies:
    custom-event "~1.0.0"
    ent "~2.2.0"
    extend "^3.0.0"
    void-elements "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/domain-browser/-/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"

ecc-jsbn@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz#0fc73a9ed5f0d53c38193398523ef7e543777505"
  dependencies:
    jsbn "~0.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"

encodeurl@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"

end-of-stream@^1.0.0:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  dependencies:
    once "^1.4.0"

engine.io-client@~1.8.4:
  version "1.8.5"
  resolved "https://registry.yarnpkg.com/engine.io-client/-/engine.io-client-1.8.5.tgz#fe7fb60cb0dcf2fa2859489329cb5968dedeb11f"
  dependencies:
    component-emitter "1.2.1"
    component-inherit "0.0.3"
    debug "2.3.3"
    engine.io-parser "1.3.2"
    has-cors "1.1.0"
    indexof "0.0.1"
    parsejson "0.0.3"
    parseqs "0.0.5"
    parseuri "0.0.5"
    ws "~1.1.5"
    xmlhttprequest-ssl "1.5.3"
    yeast "0.1.2"

engine.io-parser@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/engine.io-parser/-/engine.io-parser-1.3.2.tgz#937b079f0007d0893ec56d46cb220b8cb435220a"
  dependencies:
    after "0.8.2"
    arraybuffer.slice "0.0.6"
    base64-arraybuffer "0.1.5"
    blob "0.0.4"
    has-binary "0.1.7"
    wtf-8 "1.0.0"

engine.io@~1.8.4:
  version "1.8.5"
  resolved "https://registry.yarnpkg.com/engine.io/-/engine.io-1.8.5.tgz#4ebe5e75c6dc123dee4afdce6e5fdced21eb93f6"
  dependencies:
    accepts "1.3.3"
    base64id "1.0.0"
    cookie "0.3.1"
    debug "2.3.3"
    engine.io-parser "1.3.2"
    ws "~1.1.5"

enhanced-resolve@~0.9.0:
  version "0.9.1"
  resolved "https://registry.yarnpkg.com/enhanced-resolve/-/enhanced-resolve-0.9.1.tgz#4d6e689b3725f86090927ccc86cd9f1635b89e2e"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

ent@~2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/ent/-/ent-2.2.0.tgz#e964219325a21d05f44466a2f686ed6ce5f5dd1d"

errno@^0.1.3:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/errno/-/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.1.tgz#f855a86ce61adc4e8621c3cda21e7a7612c3a8dc"
  dependencies:
    is-arrayish "^0.2.1"

es5-ext@^0.10.14, es5-ext@^0.10.35, es5-ext@^0.10.9, es5-ext@~0.10.14:
  version "0.10.39"
  resolved "https://registry.yarnpkg.com/es5-ext/-/es5-ext-0.10.39.tgz#fca21b67559277ca4ac1a1ed7048b107b6f76d87"
  dependencies:
    es6-iterator "~2.0.3"
    es6-symbol "~3.1.1"

es6-iterator@^2.0.1, es6-iterator@~2.0.1, es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/es6-iterator/-/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/es6-map/-/es6-map-0.1.5.tgz#9136e0503dcc06a301690f0bb14ff4e364e949f0"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-promise@^4.0.3:
  version "4.2.4"
  resolved "https://registry.yarnpkg.com/es6-promise/-/es6-promise-4.2.4.tgz#dc4221c2b16518760bd8c39a52d8f356fc00ed29"

es6-set@~0.1.5:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/es6-set/-/es6-set-0.1.5.tgz#d2b3ec5d4d800ced818db538d28974db0a73ccb1"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1, es6-symbol@^3.1.1, es6-symbol@~3.1.1:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/es6-symbol/-/es6-symbol-3.1.1.tgz#bf00ef4fdab6ba1b46ecb7b629b4c7ed5715cc77"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-weak-map@^2.0.1:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/es6-weak-map/-/es6-weak-map-2.0.2.tgz#5e3ab32251ffd1538a1f8e5ffa1357772f92d96f"
  dependencies:
    d "1"
    es5-ext "^0.10.14"
    es6-iterator "^2.0.1"
    es6-symbol "^3.1.1"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.2.tgz#4dbc2fe674e71949caf3fb2695ce7f2dc1d9a8d1"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.3, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escodegen@1.7.x:
  version "1.7.1"
  resolved "https://registry.yarnpkg.com/escodegen/-/escodegen-1.7.1.tgz#30ecfcf66ca98dc67cd2fd162abeb6eafa8ce6fc"
  dependencies:
    esprima "^1.2.2"
    estraverse "^1.9.1"
    esutils "^2.0.2"
    optionator "^0.5.0"
  optionalDependencies:
    source-map "~0.2.0"

escope@^3.3.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/escope/-/escope-3.6.0.tgz#e01975e812781a163a6dadfdd80398dc64c889c3"
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint@^1.5.1, eslint@^1.6.0:
  version "1.10.3"
  resolved "https://registry.yarnpkg.com/eslint/-/eslint-1.10.3.tgz#fb19a91b13c158082bbca294b17d979bc8353a0a"
  dependencies:
    chalk "^1.0.0"
    concat-stream "^1.4.6"
    debug "^2.1.1"
    doctrine "^0.7.1"
    escape-string-regexp "^1.0.2"
    escope "^3.3.0"
    espree "^2.2.4"
    estraverse "^4.1.1"
    estraverse-fb "^1.3.1"
    esutils "^2.0.2"
    file-entry-cache "^1.1.1"
    glob "^5.0.14"
    globals "^8.11.0"
    handlebars "^4.0.0"
    inquirer "^0.11.0"
    is-my-json-valid "^2.10.0"
    is-resolvable "^1.0.0"
    js-yaml "3.4.5"
    json-stable-stringify "^1.0.0"
    lodash.clonedeep "^3.0.1"
    lodash.merge "^3.3.2"
    lodash.omit "^3.1.0"
    minimatch "^3.0.0"
    mkdirp "^0.5.0"
    object-assign "^4.0.1"
    optionator "^0.6.0"
    path-is-absolute "^1.0.0"
    path-is-inside "^1.0.1"
    shelljs "^0.5.3"
    strip-json-comments "~1.0.1"
    text-table "~0.2.0"
    user-home "^2.0.0"
    xml-escape "~1.0.0"

espree@^2.2.4:
  version "2.2.5"
  resolved "https://registry.yarnpkg.com/espree/-/espree-2.2.5.tgz#df691b9310889402aeb29cc066708c56690b854b"

esprima@2.5.x:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-2.5.0.tgz#f387a46fd344c1b1a39baf8c20bfb43b6d0058cc"

esprima@^1.2.2:
  version "1.2.5"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-1.2.5.tgz#0993502feaf668138325756f30f9a51feeec11e9"

esprima@^2.6.0:
  version "2.7.3"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"

esprima@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.0.tgz#4499eddcd1110e0b218bacf2fa7f7f59f55ca804"

"esprima@~ 1.0.2":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-1.0.4.tgz#9f557e08fc3b4d26ece9dd34f8fbf476b62585ad"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  dependencies:
    estraverse "^4.1.0"

estraverse-fb@^1.3.1:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/estraverse-fb/-/estraverse-fb-1.3.2.tgz#d323a4cb5e5ac331cea033413a9253e1643e07c4"

estraverse@^1.9.1:
  version "1.9.3"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-1.9.3.tgz#af67f2dc922582415950926091a4005d29c9bb44"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

esutils@^1.1.6:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-1.1.6.tgz#c01ccaa9ae4b897c6d0c3e210ae52f3c7a844375"

esutils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.yarnpkg.com/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"

event-emitter@~0.3.5:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/event-emitter/-/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter2@~0.4.13:
  version "0.4.14"
  resolved "https://registry.yarnpkg.com/eventemitter2/-/eventemitter2-0.4.14.tgz#8f61b75cde012b2e9eb284d4545583b5643b61ab"

eventemitter3@1.x.x:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/eventemitter3/-/eventemitter3-1.2.0.tgz#1c86991d816ad1e504750e73874224ecf3bec508"

events-to-array@^1.0.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/events-to-array/-/events-to-array-1.1.2.tgz#2d41f563e1fe400ed4962fe1a4d5c6a7539df7f6"

events@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"

eventsource@0.1.6:
  version "0.1.6"
  resolved "https://registry.yarnpkg.com/eventsource/-/eventsource-0.1.6.tgz#0acede849ed7dd1ccc32c811bb11b944d4f29232"
  dependencies:
    original ">=0.0.5"

execa@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/execa/-/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/exit-hook/-/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"

exit@~0.1.1:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/exit/-/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"

expand-braces@^0.1.1:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/expand-braces/-/expand-braces-0.1.2.tgz#488b1d1d2451cb3d3a6b192cfc030f44c5855fea"
  dependencies:
    array-slice "^0.2.3"
    array-unique "^0.2.1"
    braces "^0.1.2"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-range@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/expand-range/-/expand-range-0.1.1.tgz#4cb8eda0993ca56fa4f41fc42f3cbb4ccadff044"
  dependencies:
    is-number "^0.1.1"
    repeat-string "^0.2.2"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

express@^4.13.3:
  version "4.16.2"
  resolved "https://registry.yarnpkg.com/express/-/express-4.16.2.tgz#e35c6dfe2d64b7dca0a5cd4f21781be3299e076c"
  dependencies:
    accepts "~1.3.4"
    array-flatten "1.1.1"
    body-parser "1.18.2"
    content-disposition "0.5.2"
    content-type "~1.0.4"
    cookie "0.3.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.1"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.1.0"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.2"
    qs "6.5.1"
    range-parser "~1.2.0"
    safe-buffer "5.1.1"
    send "0.16.1"
    serve-static "1.13.1"
    setprototypeof "1.1.0"
    statuses "~1.3.1"
    type-is "~1.6.15"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend@3, extend@^3.0.0, extend@~3.0.0, extend@~3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/extend/-/extend-3.0.1.tgz#a755ea7bc1adfcc5a31ce7e762dbaadc5e636444"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extract-zip@^1.6.5:
  version "1.6.6"
  resolved "https://registry.yarnpkg.com/extract-zip/-/extract-zip-1.6.6.tgz#1290ede8d20d0872b429fd3f351ca128ec5ef85c"
  dependencies:
    concat-stream "1.6.0"
    debug "2.6.9"
    mkdirp "0.5.0"
    yauzl "2.4.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"

eyes@0.1.x:
  version "0.1.8"
  resolved "https://registry.yarnpkg.com/eyes/-/eyes-0.1.8.tgz#62cf120234c683785d902348a800ef3e0cc20bc0"

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz#c053477817c86b51daa853c81e059b733d023614"

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"

fast-levenshtein@~1.0.0, fast-levenshtein@~1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-1.0.7.tgz#0178dcdee023b92905193af0959e8a7639cfdcb9"

faye-websocket@^0.10.0, faye-websocket@~0.10.0:
  version "0.10.0"
  resolved "https://registry.yarnpkg.com/faye-websocket/-/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.0:
  version "0.11.1"
  resolved "https://registry.yarnpkg.com/faye-websocket/-/faye-websocket-0.11.1.tgz#f0efe18c4f56e4f40afc7e06c719fd5ee6188f38"
  dependencies:
    websocket-driver ">=0.5.1"

fd-slicer@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/fd-slicer/-/fd-slicer-1.0.1.tgz#8b5bcbd9ec327c5041bf9ab023fd6750f1177e65"
  dependencies:
    pend "~1.2.0"

figures@^1.0.1, figures@^1.3.5:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

file-entry-cache@^1.1.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-1.3.1.tgz#44c61ea607ae4be9c1402f41f44270cbfe334ff8"
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-sync-cmp@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/file-sync-cmp/-/file-sync-cmp-0.1.1.tgz#a5e7a8ffbfa493b43b923bbd4ca89a53b63b612b"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/filename-regex/-/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"

fileset@0.2.x:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/fileset/-/fileset-0.2.1.tgz#588ef8973c6623b2a76df465105696b96aac8067"
  dependencies:
    glob "5.x"
    minimatch "2.x"

fill-range@^2.1.0:
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-2.2.3.tgz#50b77dfd7e469bc7492470963699fe7a8485a723"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^1.1.3"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

finalhandler@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/finalhandler/-/finalhandler-1.1.0.tgz#ce0b6855b45853e791b2fcc680046d88253dd7f5"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    statuses "~1.3.1"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

findup-sync@~0.1.0, findup-sync@~0.1.2:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/findup-sync/-/findup-sync-0.1.3.tgz#7f3e7a97b82392c653bf06589bd85190e93c3683"
  dependencies:
    glob "~3.2.9"
    lodash "~2.4.1"

flat-cache@^1.2.1:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-1.3.0.tgz#d3030b32b38154f4e3b7e9c709f490f7ef97c481"
  dependencies:
    circular-json "^0.3.1"
    del "^2.0.2"
    graceful-fs "^4.1.2"
    write "^0.2.1"

for-in@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  dependencies:
    for-in "^1.0.1"

foreground-child@^1.3.3, foreground-child@^1.5.3, foreground-child@^1.5.6:
  version "1.5.6"
  resolved "https://registry.yarnpkg.com/foreground-child/-/foreground-child-1.5.6.tgz#4fd71ad2dfde96789b980a5c0a295937cb2f5ce9"
  dependencies:
    cross-spawn "^4"
    signal-exit "^3.0.0"

forever-agent@~0.6.0, forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-0.2.0.tgz#26f8bc26da6440e299cbdcfb69035c4f77a6e466"
  dependencies:
    async "~0.9.0"
    combined-stream "~0.0.4"
    mime-types "~2.0.3"

form-data@~2.1.1:
  version "2.1.4"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.1.4.tgz#33c183acf193276ecaa98143a69e94bfee1750d1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.12"

form-data@~2.3.1:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.3.2.tgz#4970498be604c20c005d4f5c23aecd21d6b49099"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "1.0.6"
    mime-types "^2.1.12"

formidable@1.0.11:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/formidable/-/formidable-1.0.11.tgz#68f63325a035e644b6f7bb3d11243b9761de1b30"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/forwarded/-/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"

fresh@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.1.0.tgz#03e4b0178424e4c2d5d19a54d8814cdc97934850"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"

fs-exists-cached@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs-exists-cached/-/fs-exists-cached-1.0.0.tgz#cf25554ca050dc49ae6656b41de42258989dcbce"

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.0.0:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-1.1.3.tgz#11f82318f5fe7bb2cd22965a108e9306208216d8"
  dependencies:
    nan "^2.3.0"
    node-pre-gyp "^0.6.39"

fstream-ignore@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/fstream-ignore/-/fstream-ignore-1.0.5.tgz#9c31dae34767018fe1d249b24dada67d092da105"
  dependencies:
    fstream "^1.0.0"
    inherits "2"
    minimatch "^3.0.0"

fstream@^1.0.0, fstream@^1.0.10, fstream@^1.0.2:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/fstream/-/fstream-1.0.11.tgz#5c1fb1f117477114f0632a0eb4b71b3cb0fd3171"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-loop@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/function-loop/-/function-loop-1.0.1.tgz#8076bb305e8e6a3cceee2920765f330d190f340c"

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.yarnpkg.com/gauge/-/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/gaze/-/gaze-1.1.2.tgz#847224677adb8870d679257ed3388fdb61e40105"
  dependencies:
    globule "^1.0.0"

generate-function@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/generate-function/-/generate-function-2.0.0.tgz#6858fe7c0969b7d4e9093337647ac79f60dfbe74"

generate-object-property@^1.1.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/generate-object-property/-/generate-object-property-1.2.0.tgz#9c0e1c40308ce804f4783618b937fa88f99d50d0"
  dependencies:
    is-property "^1.0.0"

get-caller-file@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/get-caller-file/-/get-caller-file-1.0.2.tgz#f702e63127e7e231c160a80c1554acb70d5047e5"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/get-stdin/-/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"

getobject@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/getobject/-/getobject-0.1.0.tgz#047a449789fa160d018f5486ed91320b6ec7885c"

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob-whatev@~0.1.4:
  version "0.1.8"
  resolved "https://registry.yarnpkg.com/glob-whatev/-/glob-whatev-0.1.8.tgz#a33a763262e501e851bc84fd22b5736cff3826fd"
  dependencies:
    minimatch "~0.2.5"

glob@3.2.11, glob@~3.2.9:
  version "3.2.11"
  resolved "https://registry.yarnpkg.com/glob/-/glob-3.2.11.tgz#4a973f635b9190f715d10987d5c00fd2815ebe3d"
  dependencies:
    inherits "2"
    minimatch "0.3"

glob@5.x, glob@^5.0.14:
  version "5.0.15"
  resolved "https://registry.yarnpkg.com/glob/-/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

"glob@>= 3.1.4", glob@^7.0.0, glob@^7.0.3, glob@^7.0.5, glob@^7.0.6, glob@~7.1.1:
  version "7.1.2"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~3.1.21:
  version "3.1.21"
  resolved "https://registry.yarnpkg.com/glob/-/glob-3.1.21.tgz#d29e0a055dea5138f4d07ed40e8982e83c2066cd"
  dependencies:
    graceful-fs "~1.2.0"
    inherits "1"
    minimatch "~0.2.11"

glob@~4.3.0:
  version "4.3.5"
  resolved "https://registry.yarnpkg.com/glob/-/glob-4.3.5.tgz#80fbb08ca540f238acce5d11d1e9bc41e75173d3"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "^2.0.1"
    once "^1.3.0"

globals@^8.11.0:
  version "8.18.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-8.18.0.tgz#93d4a62bdcac38cfafafc47d6b034768cb0ffcb4"

globals@^9.18.0:
  version "9.18.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

globby@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-5.0.0.tgz#ebd84667ca0dbb330b99bcfc68eac2bc54370e0d"
  dependencies:
    array-union "^1.0.1"
    arrify "^1.0.0"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globule@^1.0.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/globule/-/globule-1.2.0.tgz#1dc49c6822dd9e8a2fa00ba2a295006e8664bd09"
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.4"
    minimatch "~3.0.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
  version "4.1.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

graceful-fs@~1.2.0:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-1.2.3.tgz#15a4806a57547cb2d2dbf27f42e89a8c3451b364"

growl@1.9.2:
  version "1.9.2"
  resolved "https://registry.yarnpkg.com/growl/-/growl-1.9.2.tgz#0ea7743715db8d8de2c5ede1775e1b45ac85c02f"

grunt-babel@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/grunt-babel/-/grunt-babel-6.0.0.tgz#378189b487de1168c4c4a9fc88dd6005b35df960"
  dependencies:
    babel-core "^6.0.12"

grunt-clean@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/grunt-clean/-/grunt-clean-0.4.0.tgz#a7b4e188d7e94ca6c93bb88ec64096534931c40b"
  dependencies:
    grunt "~0.3.9"

grunt-cli@^0.1.13:
  version "0.1.13"
  resolved "https://registry.yarnpkg.com/grunt-cli/-/grunt-cli-0.1.13.tgz#e9ebc4047631f5012d922770c39378133cad10f4"
  dependencies:
    findup-sync "~0.1.0"
    nopt "~1.0.10"
    resolve "~0.3.1"

grunt-contrib-clean@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/grunt-contrib-clean/-/grunt-contrib-clean-1.1.0.tgz#564abf2d0378a983a15b9e3f30ee75b738c40638"
  dependencies:
    async "^1.5.2"
    rimraf "^2.5.1"

grunt-contrib-copy@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/grunt-contrib-copy/-/grunt-contrib-copy-1.0.0.tgz#7060c6581e904b8ab0d00f076e0a8f6e3e7c3573"
  dependencies:
    chalk "^1.1.1"
    file-sync-cmp "^0.1.0"

grunt-contrib-uglify@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/grunt-contrib-uglify/-/grunt-contrib-uglify-1.0.2.tgz#ae67a46f9153edd4cb11813a55eb69c70d7db2fb"
  dependencies:
    chalk "^1.0.0"
    lodash "^4.0.1"
    maxmin "^1.1.0"
    uglify-js "~2.6.2"
    uri-path "^1.0.0"

grunt-contrib-watch@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/grunt-contrib-watch/-/grunt-contrib-watch-1.0.0.tgz#84a1a7a1d6abd26ed568413496c73133e990018f"
  dependencies:
    async "^1.5.0"
    gaze "^1.0.0"
    lodash "^3.10.1"
    tiny-lr "^0.2.1"

grunt-eslint@^17.3.1:
  version "17.3.2"
  resolved "https://registry.yarnpkg.com/grunt-eslint/-/grunt-eslint-17.3.2.tgz#36a8b3be6ccde88c8b58f909745d75db19e5d4b0"
  dependencies:
    chalk "^1.0.0"
    eslint "^1.5.1"

grunt-karma@^0.12.1:
  version "0.12.2"
  resolved "https://registry.yarnpkg.com/grunt-karma/-/grunt-karma-0.12.2.tgz#d52676ab94779e4b20052b5f3519eb32653dc566"
  dependencies:
    lodash "^3.10.1"

grunt-legacy-log-utils@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/grunt-legacy-log-utils/-/grunt-legacy-log-utils-0.1.1.tgz#c0706b9dd9064e116f36f23fe4e6b048672c0f7e"
  dependencies:
    colors "~0.6.2"
    lodash "~2.4.1"
    underscore.string "~2.3.3"

grunt-legacy-log@~0.1.0:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/grunt-legacy-log/-/grunt-legacy-log-0.1.3.tgz#ec29426e803021af59029f87d2f9cd7335a05531"
  dependencies:
    colors "~0.6.2"
    grunt-legacy-log-utils "~0.1.1"
    hooker "~0.2.3"
    lodash "~2.4.1"
    underscore.string "~2.3.3"

grunt-legacy-util@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/grunt-legacy-util/-/grunt-legacy-util-0.2.0.tgz#93324884dbf7e37a9ff7c026dff451d94a9e554b"
  dependencies:
    async "~0.1.22"
    exit "~0.1.1"
    getobject "~0.1.0"
    hooker "~0.2.3"
    lodash "~0.9.2"
    underscore.string "~2.2.1"
    which "~1.0.5"

grunt-mocha-istanbul@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/grunt-mocha-istanbul/-/grunt-mocha-istanbul-3.0.1.tgz#a33525707b2fa82eb2f7fb3230513f7ca279bf60"

grunt-mocha-test@^0.12.7:
  version "0.12.7"
  resolved "https://registry.yarnpkg.com/grunt-mocha-test/-/grunt-mocha-test-0.12.7.tgz#c61cdf32a6762954115fe712b983e3dd8e0c9554"
  dependencies:
    hooker "~0.2.3"
    mkdirp "^0.5.0"

grunt-webpack@^1.0.11:
  version "1.0.18"
  resolved "https://registry.yarnpkg.com/grunt-webpack/-/grunt-webpack-1.0.18.tgz#ff26c43ff35bae6cca707a93c4bcdd950a3ecbb7"
  dependencies:
    lodash "^4.7.0"

grunt@^0.4.5:
  version "0.4.5"
  resolved "https://registry.yarnpkg.com/grunt/-/grunt-0.4.5.tgz#56937cd5194324adff6d207631832a9d6ba4e7f0"
  dependencies:
    async "~0.1.22"
    coffee-script "~1.3.3"
    colors "~0.6.2"
    dateformat "1.0.2-1.2.3"
    eventemitter2 "~0.4.13"
    exit "~0.1.1"
    findup-sync "~0.1.2"
    getobject "~0.1.0"
    glob "~3.1.21"
    grunt-legacy-log "~0.1.0"
    grunt-legacy-util "~0.2.0"
    hooker "~0.2.3"
    iconv-lite "~0.2.11"
    js-yaml "~2.0.5"
    lodash "~0.9.2"
    minimatch "~0.2.12"
    nopt "~1.0.10"
    rimraf "~2.2.8"
    underscore.string "~2.2.1"
    which "~1.0.5"

grunt@~0.3.9:
  version "0.3.17"
  resolved "https://registry.yarnpkg.com/grunt/-/grunt-0.3.17.tgz#f2e034d200befd5eeb38ba5c41d4ccd7235fd64d"
  dependencies:
    async "~0.1.18"
    colors "~0.6.0"
    connect "~2.4.4"
    dateformat "1.0.2-1.2.3"
    glob-whatev "~0.1.4"
    gzip-js "~0.3.1"
    hooker "~0.2.3"
    jshint "~0.9.1"
    nodeunit "~0.7.4"
    nopt "~1.0.10"
    prompt "~0.1.12"
    semver "~1.0.13"
    temporary "~0.0.4"
    uglify-js "~1.3.3"
    underscore "~1.2.4"
    underscore.string "~2.1.1"

gzip-js@~0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/gzip-js/-/gzip-js-0.3.2.tgz#23117efeeb28cf385248deff0dffad894836d96b"
  dependencies:
    crc32 ">= 0.2.2"
    deflate-js ">= 0.2.2"

gzip-size@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/gzip-size/-/gzip-size-1.0.0.tgz#66cf8b101047227b95bace6ea1da0c177ed5c22f"
  dependencies:
    browserify-zlib "^0.1.4"
    concat-stream "^1.4.1"

handlebars@^4.0.0, handlebars@^4.0.1, handlebars@^4.0.3:
  version "4.0.11"
  resolved "https://registry.yarnpkg.com/handlebars/-/handlebars-4.0.11.tgz#630a35dfe0294bc281edae6ffc5d329fc7982dcc"
  dependencies:
    async "^1.4.0"
    optimist "^0.6.1"
    source-map "^0.4.4"
  optionalDependencies:
    uglify-js "^2.6"

har-schema@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/har-schema/-/har-schema-1.0.5.tgz#d263135f43307c02c602afc8fe95970c0151369e"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"

har-validator@^1.4.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-1.8.0.tgz#d83842b0eb4c435960aeb108a067a3aa94c0eeb2"
  dependencies:
    bluebird "^2.9.30"
    chalk "^1.0.0"
    commander "^2.8.1"
    is-my-json-valid "^2.12.0"

har-validator@~2.0.6:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-2.0.6.tgz#cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d"
  dependencies:
    chalk "^1.1.1"
    commander "^2.9.0"
    is-my-json-valid "^2.12.4"
    pinkie-promise "^2.0.0"

har-validator@~4.2.1:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-4.2.1.tgz#33481d0f1bbff600dd203d75812a6a5fba002e2a"
  dependencies:
    ajv "^4.9.1"
    har-schema "^1.0.5"

har-validator@~5.0.3:
  version "5.0.3"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-5.0.3.tgz#ba402c266194f15956ef15e0fcf242993f6a7dfd"
  dependencies:
    ajv "^5.1.0"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-binary@0.1.7:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/has-binary/-/has-binary-0.1.7.tgz#68e61eb16210c9545a0a5cce06a873912fe1e68c"
  dependencies:
    isarray "0.0.1"

has-cors@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/has-cors/-/has-cors-1.1.0.tgz#5e474793f7ea9843d1bb99c23eef49ff126fff39"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

hasha@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/hasha/-/hasha-2.2.0.tgz#78d7cbfc1e6d66303fe79837365984517b2f6ee1"
  dependencies:
    is-stream "^1.0.1"
    pinkie-promise "^2.0.0"

hawk@3.1.3, hawk@~3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/hawk/-/hawk-3.1.3.tgz#078444bd7c1640b0fe540d2c9b73d59678e8e1c4"
  dependencies:
    boom "2.x.x"
    cryptiles "2.x.x"
    hoek "2.x.x"
    sntp "1.x.x"

hawk@~2.3.0:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/hawk/-/hawk-2.3.1.tgz#1e731ce39447fa1d0f6d707f7bceebec0fd1ec1f"
  dependencies:
    boom "2.x.x"
    cryptiles "2.x.x"
    hoek "2.x.x"
    sntp "1.x.x"

hawk@~6.0.2:
  version "6.0.2"
  resolved "https://registry.yarnpkg.com/hawk/-/hawk-6.0.2.tgz#af4d914eb065f9b5ce4d9d11c1cb2126eecc3038"
  dependencies:
    boom "4.x.x"
    cryptiles "3.x.x"
    hoek "4.x.x"
    sntp "2.x.x"

hoek@2.x.x:
  version "2.16.3"
  resolved "https://registry.yarnpkg.com/hoek/-/hoek-2.16.3.tgz#20bb7403d3cea398e91dc4710a8ff1b8274a25ed"

hoek@4.x.x:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/hoek/-/hoek-4.2.1.tgz#9634502aa12c445dd5a7c5734b572bb8738aacbb"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/home-or-tmp/-/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hooker@~0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/hooker/-/hooker-0.2.3.tgz#b834f723cc4a242aa65963459df6d984c5d3d959"

hosted-git-info@^2.1.4:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/hosted-git-info/-/hosted-git-info-2.5.0.tgz#6d60e34b3abbc8313062c3b798ef8d901a07af3c"

http-errors@1.6.2, http-errors@~1.6.2:
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.6.2.tgz#0a002cc85707192a7e7946ceedc11155f60ec736"
  dependencies:
    depd "1.1.1"
    inherits "2.0.3"
    setprototypeof "1.0.3"
    statuses ">= 1.3.1 < 2"

http-errors@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.3.1.tgz#197e22cdebd4198585e8694ef6786197b91ed942"
  dependencies:
    inherits "~2.0.1"
    statuses "1"

http-parser-js@>=0.4.0:
  version "0.4.10"
  resolved "https://registry.yarnpkg.com/http-parser-js/-/http-parser-js-0.4.10.tgz#92c9c1374c35085f75db359ec56cc257cbb93fa4"

http-proxy-middleware@~0.17.1:
  version "0.17.4"
  resolved "https://registry.yarnpkg.com/http-proxy-middleware/-/http-proxy-middleware-0.17.4.tgz#642e8848851d66f09d4f124912846dbaeb41b833"
  dependencies:
    http-proxy "^1.16.2"
    is-glob "^3.1.0"
    lodash "^4.17.2"
    micromatch "^2.3.11"

http-proxy@^1.13.0, http-proxy@^1.16.2:
  version "1.16.2"
  resolved "https://registry.yarnpkg.com/http-proxy/-/http-proxy-1.16.2.tgz#06dff292952bf64dbe8471fa9df73066d4f37742"
  dependencies:
    eventemitter3 "1.x.x"
    requires-port "1.x.x"

http-signature@~0.10.0:
  version "0.10.1"
  resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-0.10.1.tgz#4fbdac132559aa8323121e540779c0a012b27e66"
  dependencies:
    asn1 "0.1.11"
    assert-plus "^0.1.5"
    ctype "0.5.3"

http-signature@~1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-1.1.1.tgz#df72e267066cd0ac67fb76adf8e134a8fbcf91bf"
  dependencies:
    assert-plus "^0.2.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/https-browserify/-/https-browserify-0.0.1.tgz#3f91365cabe60b77ed0ebba24b454e3e09d95a82"

https-proxy-agent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz#35f7da6c48ce4ddbfa264891ac593ee5ff8671e6"
  dependencies:
    agent-base "2"
    debug "2"
    extend "3"

iconv-lite@0.4.13:
  version "0.4.13"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.13.tgz#1f88aba4ab0b1508e8312acc39345f36e992e2f2"

iconv-lite@0.4.19:
  version "0.4.19"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.19.tgz#f7468f60135f5e5dad3399c0a81be9a1603a082b"

iconv-lite@~0.2.11:
  version "0.2.11"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.2.11.tgz#1ce60a3a57864a292d1321ff4609ca4bb965adc8"

ieee754@^1.1.4:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.8.tgz#be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/indent-string/-/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/indexof/-/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-1.0.2.tgz#ca4309dadee6b54cc0b8d247e8d7c7a0975bdc9b"

inherits@2, inherits@2.0.3, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

ini@~1.3.0:
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/ini/-/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"

inquirer@^0.11.0:
  version "0.11.4"
  resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-0.11.4.tgz#81e3374e8361beaff2d97016206d359d0b32fa4d"
  dependencies:
    ansi-escapes "^1.1.0"
    ansi-regex "^2.0.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^1.0.1"
    figures "^1.3.5"
    lodash "^3.3.1"
    readline2 "^1.0.1"
    run-async "^0.1.0"
    rx-lite "^3.1.2"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

interpret@^0.6.4:
  version "0.6.6"
  resolved "https://registry.yarnpkg.com/interpret/-/interpret-0.6.6.tgz#fecd7a18e7ce5ca6abfb953e1f86213a49f1625b"

invariant@^2.2.2:
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/invariant/-/invariant-2.2.3.tgz#1a827dfde7dcbd7c323f0ca826be8fa7c5e9d688"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

ipaddr.js@1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/ipaddr.js/-/ipaddr.js-1.6.0.tgz#e3fa357b773da619f26e95f049d055c72796f86b"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-builtin-module/-/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/is-dotfile/-/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-extglob@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  dependencies:
    is-extglob "^2.1.0"

is-my-ip-valid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-my-ip-valid/-/is-my-ip-valid-1.0.0.tgz#7b351b8e8edd4d3995d4d066680e664d94696824"

is-my-json-valid@^2.10.0, is-my-json-valid@^2.12.0, is-my-json-valid@^2.12.4:
  version "2.17.2"
  resolved "https://registry.yarnpkg.com/is-my-json-valid/-/is-my-json-valid-2.17.2.tgz#6b2103a288e94ef3de5cf15d29dd85fc4b78d65c"
  dependencies:
    generate-function "^2.0.0"
    generate-object-property "^1.1.0"
    is-my-ip-valid "^1.0.0"
    jsonpointer "^4.0.0"
    xtend "^4.0.0"

is-number@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-0.1.1.tgz#69a7af116963d47206ec9bd9b48a14216f1e3806"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  dependencies:
    kind-of "^3.0.2"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-cwd/-/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz#6477582b8214d602346094567003be8a9eac04dc"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  dependencies:
    path-is-inside "^1.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-property@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-property/-/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-resolvable/-/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-utf8/-/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isbinaryfile@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/isbinaryfile/-/isbinaryfile-3.0.2.tgz#4a3e974ec0cba9004d3fc6cde7209ea69368a621"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isstream@~0.1.1, isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

istanbul-lib-coverage@^1.1.1, istanbul-lib-coverage@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/istanbul-lib-coverage/-/istanbul-lib-coverage-1.1.2.tgz#4113c8ff6b7a40a1ef7350b01016331f63afde14"

istanbul-lib-hook@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/istanbul-lib-hook/-/istanbul-lib-hook-1.1.0.tgz#8538d970372cb3716d53e55523dd54b557a8d89b"
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.9.1:
  version "1.9.2"
  resolved "https://registry.yarnpkg.com/istanbul-lib-instrument/-/istanbul-lib-instrument-1.9.2.tgz#84905bf47f7e0b401d6b840da7bad67086b4aab6"
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.18.0"
    istanbul-lib-coverage "^1.1.2"
    semver "^5.3.0"

istanbul-lib-report@^1.1.2:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/istanbul-lib-report/-/istanbul-lib-report-1.1.3.tgz#2df12188c0fa77990c0d2176d2d0ba3394188259"
  dependencies:
    istanbul-lib-coverage "^1.1.2"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.2.2:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.3.tgz#20fb54b14e14b3fb6edb6aca3571fd2143db44e6"
  dependencies:
    debug "^3.1.0"
    istanbul-lib-coverage "^1.1.2"
    mkdirp "^0.5.1"
    rimraf "^2.6.1"
    source-map "^0.5.3"

istanbul-reports@^1.1.3:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/istanbul-reports/-/istanbul-reports-1.1.4.tgz#5ccba5e22b7b5a5d91d5e0a830f89be334bf97bd"
  dependencies:
    handlebars "^4.0.3"

"istanbul@github:kpdecker/istanbul":
  version "0.4.0"
  resolved "https://codeload.github.com/kpdecker/istanbul/tar.gz/dd1228d2f0a6e8506cbb5dba398a8297b1dbaf22"
  dependencies:
    abbrev "1.0.x"
    async "1.x"
    convert-source-map "^1.1.1"
    escodegen "1.7.x"
    esprima "2.5.x"
    fileset "0.2.x"
    handlebars "^4.0.1"
    js-yaml "3.x"
    mkdirp "0.5.x"
    nopt "3.x"
    once "1.x"
    resolve "1.1.x"
    source-map "^0.4.4"
    source-map-support "^0.3.2"
    supports-color "^3.1.0"
    which "^1.1.1"
    wordwrap "^1.0.0"

jade@0.26.3:
  version "0.26.3"
  resolved "https://registry.yarnpkg.com/jade/-/jade-0.26.3.tgz#8f10d7977d8d79f2f6ff862a81b0513ccb25686c"
  dependencies:
    commander "0.6.1"
    mkdirp "0.3.0"

js-tokens@^3.0.0, js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

js-yaml@3.4.5:
  version "3.4.5"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.4.5.tgz#c3403797df12b91866574f2de23646fe8cafb44d"
  dependencies:
    argparse "^1.0.2"
    esprima "^2.6.0"

js-yaml@3.6.1:
  version "3.6.1"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.6.1.tgz#6e5fe67d8b205ce4d22fad05b7781e8dadcc4b30"
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

js-yaml@3.x, js-yaml@^3.10.0, js-yaml@^3.2.7, js-yaml@^3.3.1:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.10.0.tgz#2e78441646bd4682e963f22b6e92823c309c62dc"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@~2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-2.0.5.tgz#a25ae6509999e97df278c6719da11bd0687743a8"
  dependencies:
    argparse "~ 0.1.11"
    esprima "~ 1.0.2"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

jshint@~0.9.1:
  version "0.9.1"
  resolved "https://registry.yarnpkg.com/jshint/-/jshint-0.9.1.tgz#ff32ec7f09f84001f7498eeafd63c9e4fbb2dc0e"
  dependencies:
    cli "0.4.3"
    minimatch "0.0.x"

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stable-stringify@^1.0.0, json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.0, json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json3@3.3.2, json3@^3.3.2:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/json3/-/json3-3.3.2.tgz#3c0434743df93e2f5c42aee7b19bcb483575f4e1"

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

jsonpointer@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/jsonpointer/-/jsonpointer-4.0.1.tgz#4fd92cb34e0e9db3c89c8622ecf51f9b978c6cb9"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/jsprim/-/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

karma-mocha-reporter@^2.0.0:
  version "2.2.5"
  resolved "https://registry.yarnpkg.com/karma-mocha-reporter/-/karma-mocha-reporter-2.2.5.tgz#15120095e8ed819186e47a0b012f3cd741895560"
  dependencies:
    chalk "^2.1.0"
    log-symbols "^2.1.0"
    strip-ansi "^4.0.0"

karma-mocha@^0.2.0:
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/karma-mocha/-/karma-mocha-0.2.2.tgz#388ed917da15dcb196d1b915c1934ef803193f8e"

karma-phantomjs-launcher@^1.0.0:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/karma-phantomjs-launcher/-/karma-phantomjs-launcher-1.0.4.tgz#d23ca34801bda9863ad318e3bb4bd4062b13acd2"
  dependencies:
    lodash "^4.0.1"
    phantomjs-prebuilt "^2.1.7"

karma-sauce-launcher@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/karma-sauce-launcher/-/karma-sauce-launcher-0.3.1.tgz#fa41f6afd1ad6cb7610885da83cbc9921a4d334c"
  dependencies:
    q "^1.4.1"
    sauce-connect-launcher "^0.13.0"
    saucelabs "^1.0.1"
    wd "^0.3.4"

karma-sourcemap-loader@^0.3.6:
  version "0.3.7"
  resolved "https://registry.yarnpkg.com/karma-sourcemap-loader/-/karma-sourcemap-loader-0.3.7.tgz#91322c77f8f13d46fed062b042e1009d4c4505d8"
  dependencies:
    graceful-fs "^4.1.2"

karma-webpack@^1.7.0:
  version "1.8.1"
  resolved "https://registry.yarnpkg.com/karma-webpack/-/karma-webpack-1.8.1.tgz#39d5fd2edeea3cc3ef5b405989b37d5b0e6a3b4e"
  dependencies:
    async "~0.9.0"
    loader-utils "^0.2.5"
    lodash "^3.8.0"
    source-map "^0.1.41"
    webpack-dev-middleware "^1.0.11"

karma@^0.13.11:
  version "0.13.22"
  resolved "https://registry.yarnpkg.com/karma/-/karma-0.13.22.tgz#07750b1bd063d7e7e7b91bcd2e6354d8f2aa8744"
  dependencies:
    batch "^0.5.3"
    bluebird "^2.9.27"
    body-parser "^1.12.4"
    chokidar "^1.4.1"
    colors "^1.1.0"
    connect "^3.3.5"
    core-js "^2.1.0"
    di "^0.0.1"
    dom-serialize "^2.2.0"
    expand-braces "^0.1.1"
    glob "^7.0.0"
    graceful-fs "^4.1.2"
    http-proxy "^1.13.0"
    isbinaryfile "^3.0.0"
    lodash "^3.8.0"
    log4js "^0.6.31"
    mime "^1.3.4"
    minimatch "^3.0.0"
    optimist "^0.6.1"
    rimraf "^2.3.3"
    socket.io "^1.4.5"
    source-map "^0.5.3"
    useragent "^2.1.6"

kew@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/kew/-/kew-0.7.0.tgz#79d93d2d33363d6fdd2970b335d9141ad591d79b"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  dependencies:
    is-buffer "^1.1.5"

klaw@^1.0.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/klaw/-/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  optionalDependencies:
    graceful-fs "^4.1.9"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"

lazystream@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/lazystream/-/lazystream-0.1.0.tgz#1b25d63c772a4c20f0a5ed0a9d77f484b6e16920"
  dependencies:
    readable-stream "~1.0.2"

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

lcov-parse@0.0.10:
  version "0.0.10"
  resolved "https://registry.yarnpkg.com/lcov-parse/-/lcov-parse-0.0.10.tgz#1b0b8ff9ac9c7889250582b70b71315d9da6d9a3"

levn@~0.2.5:
  version "0.2.5"
  resolved "https://registry.yarnpkg.com/levn/-/levn-0.2.5.tgz#ba8d339d0ca4a610e3a3f145b9caf48807155054"
  dependencies:
    prelude-ls "~1.1.0"
    type-check "~0.3.1"

livereload-js@^2.2.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/livereload-js/-/livereload-js-2.3.0.tgz#c3ab22e8aaf5bf3505d80d098cbad67726548c9a"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

loader-utils@^0.2.11, loader-utils@^0.2.16, loader-utils@^0.2.5:
  version "0.2.17"
  resolved "https://registry.yarnpkg.com/loader-utils/-/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash._arraycopy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._arraycopy/-/lodash._arraycopy-3.0.0.tgz#76e7b7c1f1fb92547374878a562ed06a3e50f6e1"

lodash._arrayeach@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._arrayeach/-/lodash._arrayeach-3.0.0.tgz#bab156b2a90d3f1bbd5c653403349e5e5933ef9e"

lodash._arraymap@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._arraymap/-/lodash._arraymap-3.0.0.tgz#1a8fd0f4c0df4b61dea076d717cdc97f0a3c3e66"

lodash._baseassign@^3.0.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz#8c38a099500f215ad09e59f1722fd0c52bfe0a4e"
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash.keys "^3.0.0"

lodash._baseclone@^3.0.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/lodash._baseclone/-/lodash._baseclone-3.3.0.tgz#303519bf6393fe7e42f34d8b630ef7794e3542b7"
  dependencies:
    lodash._arraycopy "^3.0.0"
    lodash._arrayeach "^3.0.0"
    lodash._baseassign "^3.0.0"
    lodash._basefor "^3.0.0"
    lodash.isarray "^3.0.0"
    lodash.keys "^3.0.0"

lodash._basecopy@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz#8da0e6a876cf344c0ad8a54882111dd3c5c7ca36"

lodash._basedifference@^3.0.0:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/lodash._basedifference/-/lodash._basedifference-3.0.3.tgz#f2c204296c2a78e02b389081b6edcac933cf629c"
  dependencies:
    lodash._baseindexof "^3.0.0"
    lodash._cacheindexof "^3.0.0"
    lodash._createcache "^3.0.0"

lodash._baseflatten@^3.0.0:
  version "3.1.4"
  resolved "https://registry.yarnpkg.com/lodash._baseflatten/-/lodash._baseflatten-3.1.4.tgz#0770ff80131af6e34f3b511796a7ba5214e65ff7"
  dependencies:
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash._basefor@^3.0.0:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/lodash._basefor/-/lodash._basefor-3.0.3.tgz#7550b4e9218ef09fad24343b612021c79b4c20c2"

lodash._baseindexof@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/lodash._baseindexof/-/lodash._baseindexof-3.1.0.tgz#fe52b53a1c6761e42618d654e4a25789ed61822c"

lodash._bindcallback@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz#e531c27644cf8b57a99e17ed95b35c748789392e"

lodash._cacheindexof@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/lodash._cacheindexof/-/lodash._cacheindexof-3.0.2.tgz#3dc69ac82498d2ee5e3ce56091bafd2adc7bde92"

lodash._createassigner@^3.0.0:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/lodash._createassigner/-/lodash._createassigner-3.1.1.tgz#838a5bae2fdaca63ac22dee8e19fa4e6d6970b11"
  dependencies:
    lodash._bindcallback "^3.0.0"
    lodash._isiterateecall "^3.0.0"
    lodash.restparam "^3.0.0"

lodash._createcache@^3.0.0:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/lodash._createcache/-/lodash._createcache-3.1.2.tgz#56d6a064017625e79ebca6b8018e17440bdcf093"
  dependencies:
    lodash._getnative "^3.0.0"

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://registry.yarnpkg.com/lodash._getnative/-/lodash._getnative-3.9.1.tgz#570bc7dede46d61cdcde687d65d3eecbaa3aaff5"

lodash._isiterateecall@^3.0.0:
  version "3.0.9"
  resolved "https://registry.yarnpkg.com/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz#5203ad7ba425fae842460e696db9cf3e6aac057c"

lodash._pickbyarray@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/lodash._pickbyarray/-/lodash._pickbyarray-3.0.2.tgz#1f898d9607eb560b0e167384b77c7c6d108aa4c5"

lodash._pickbycallback@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash._pickbycallback/-/lodash._pickbycallback-3.0.0.tgz#ff61b9a017a7b3af7d30e6c53de28afa19b8750a"
  dependencies:
    lodash._basefor "^3.0.0"
    lodash.keysin "^3.0.0"

lodash.clonedeep@^3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/lodash.clonedeep/-/lodash.clonedeep-3.0.2.tgz#a0a1e40d82a5ea89ff5b147b8444ed63d92827db"
  dependencies:
    lodash._baseclone "^3.0.0"
    lodash._bindcallback "^3.0.0"

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/lodash.isarray/-/lodash.isarray-3.0.4.tgz#79e4eb88c36a8122af86f844aa9bcd851b5fbb55"

lodash.isplainobject@^3.0.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/lodash.isplainobject/-/lodash.isplainobject-3.2.0.tgz#9a8238ae16b200432960cd7346512d0123fbf4c5"
  dependencies:
    lodash._basefor "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.keysin "^3.0.0"

lodash.istypedarray@^3.0.0:
  version "3.0.6"
  resolved "https://registry.yarnpkg.com/lodash.istypedarray/-/lodash.istypedarray-3.0.6.tgz#c9a477498607501d8e8494d283b87c39281cef62"

lodash.keys@^3.0.0:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/lodash.keys/-/lodash.keys-3.1.2.tgz#4dbc0472b156be50a0b286855d1bd0b0c656098a"
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.keysin@^3.0.0:
  version "3.0.8"
  resolved "https://registry.yarnpkg.com/lodash.keysin/-/lodash.keysin-3.0.8.tgz#22c4493ebbedb1427962a54b445b2c8a767fb47f"
  dependencies:
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.merge@^3.3.2:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-3.3.2.tgz#0d90d93ed637b1878437bb3e21601260d7afe994"
  dependencies:
    lodash._arraycopy "^3.0.0"
    lodash._arrayeach "^3.0.0"
    lodash._createassigner "^3.0.0"
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"
    lodash.isplainobject "^3.0.0"
    lodash.istypedarray "^3.0.0"
    lodash.keys "^3.0.0"
    lodash.keysin "^3.0.0"
    lodash.toplainobject "^3.0.0"

lodash.omit@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/lodash.omit/-/lodash.omit-3.1.0.tgz#897fe382e6413d9ac97c61f78ed1e057a00af9f3"
  dependencies:
    lodash._arraymap "^3.0.0"
    lodash._basedifference "^3.0.0"
    lodash._baseflatten "^3.0.0"
    lodash._bindcallback "^3.0.0"
    lodash._pickbyarray "^3.0.0"
    lodash._pickbycallback "^3.0.0"
    lodash.keysin "^3.0.0"
    lodash.restparam "^3.0.0"

lodash.restparam@^3.0.0:
  version "3.6.1"
  resolved "https://registry.yarnpkg.com/lodash.restparam/-/lodash.restparam-3.6.1.tgz#936a4e309ef330a7645ed4145986c85ae5b20805"

lodash.toplainobject@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/lodash.toplainobject/-/lodash.toplainobject-3.0.0.tgz#28790ad942d293d78aa663a07ecf7f52ca04198d"
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash.keysin "^3.0.0"

lodash@3.10.1, lodash@^3.10.1, lodash@^3.3.1, lodash@^3.8.0:
  version "3.10.1"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-3.10.1.tgz#5bf45e8e49ba4189e17d482789dfd15bd140b7b6"

lodash@^4.0.1, lodash@^4.17.2, lodash@^4.17.4, lodash@^4.7.0, lodash@~4.17.4:
  version "4.17.5"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.5.tgz#99a92d65c0272debe8c96b6057bc8fbfa3bed511"

lodash@~0.9.2:
  version "0.9.2"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-0.9.2.tgz#8f3499c5245d346d682e5b0d3b40767e09f1a92c"

lodash@~2.4.1:
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-2.4.2.tgz#fadd834b9683073da179b3eae6d9c0d15053f73e"

lodash@~3.2.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-3.2.0.tgz#4bf50a3243f9aeb0bac41a55d3d5990675a462fb"

lodash@~3.9.3:
  version "3.9.3"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-3.9.3.tgz#0159e86832feffc6d61d852b12a953b99496bd32"

log-driver@1.2.5:
  version "1.2.5"
  resolved "https://registry.yarnpkg.com/log-driver/-/log-driver-1.2.5.tgz#7ae4ec257302fd790d557cb10c97100d857b0056"

log-symbols@^2.1.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/log-symbols/-/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  dependencies:
    chalk "^2.0.1"

log4js@^0.6.31:
  version "0.6.38"
  resolved "https://registry.yarnpkg.com/log4js/-/log4js-0.6.38.tgz#2c494116695d6fb25480943d3fc872e662a522fd"
  dependencies:
    readable-stream "~1.0.2"
    semver "~4.3.3"

"loggly@0.3.x >=0.3.7":
  version "0.3.11"
  resolved "https://registry.yarnpkg.com/loggly/-/loggly-0.3.11.tgz#62c1ec3436772f0954598f26b957d2ad2986b611"
  dependencies:
    request "2.9.x"
    timespan "2.x.x"

longest@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"

loose-envify@^1.0.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.3.1.tgz#d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848"
  dependencies:
    js-tokens "^3.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/loud-rejection/-/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lru-cache@2:
  version "2.7.3"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-2.7.3.tgz#6d4524e8b955f95d4f5b58851ce21dd72fb4e952"

lru-cache@4.1.x, lru-cache@^4.0.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.1.tgz#622e32e82488b49279114a4f9ecf45e7cd6bba55"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@~1.0.2:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-1.0.6.tgz#aa50f97047422ac72543bda177a9c9d018d98452"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

maxmin@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/maxmin/-/maxmin-1.1.0.tgz#71365e84a99dd8f8b3f7d5fde2f00d1e7f73be61"
  dependencies:
    chalk "^1.0.0"
    figures "^1.0.1"
    gzip-size "^1.0.0"
    pretty-bytes "^1.0.0"

md5-hex@^1.2.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/md5-hex/-/md5-hex-1.3.0.tgz#d2c4afe983c4370662179b8cad145219135046c4"
  dependencies:
    md5-o-matic "^0.1.1"

md5-o-matic@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/md5-o-matic/-/md5-o-matic-0.1.1.tgz#822bccd65e117c514fab176b25945d54100a03c3"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

mem@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/mem/-/mem-1.1.0.tgz#5edd52b485ca1d900fe64895505399a0dfa45f76"
  dependencies:
    mimic-fn "^1.0.0"

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.2.0.tgz#f2bb25368bc121e391c2520de92969caee0a0290"

memory-fs@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.3.0.tgz#7bcc6b629e3a43e871d7e29aca6ae8a7f15cbb20"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@~0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/memory-fs/-/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.1.0:
  version "3.7.0"
  resolved "https://registry.yarnpkg.com/meow/-/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

merge-source-map@^1.0.2:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/merge-source-map/-/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  dependencies:
    source-map "^0.6.1"

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

micromatch@^2.1.5, micromatch@^2.3.11:
  version "2.3.11"
  resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

"mime-db@>= 1.33.0 < 2", mime-db@~1.33.0:
  version "1.33.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.33.0.tgz#a3492050a5cb9b63450541e39d9788d2272783db"

mime-db@~1.12.0:
  version "1.12.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.12.0.tgz#3d0c63180f458eb10d325aaa37d7c58ae312e9d7"

mime-types@^2.1.12, mime-types@~2.1.11, mime-types@~2.1.17, mime-types@~2.1.18, mime-types@~2.1.7:
  version "2.1.18"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.18.tgz#6f323f60a83d11146f831ff11fd66e2fe5503bb8"
  dependencies:
    mime-db "~1.33.0"

mime-types@~2.0.1, mime-types@~2.0.3:
  version "2.0.14"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.0.14.tgz#310e159db23e077f8bb22b748dabfa4957140aa6"
  dependencies:
    mime-db "~1.12.0"

mime@1.2.6:
  version "1.2.6"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.2.6.tgz#b1f86c768c025fa87b48075f1709f28aeaf20365"

mime@1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.4.1.tgz#121f9ebc49e3766f311a76e1fa1c8003c4b03aa6"

mime@^1.3.4, mime@^1.5.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"

minimatch@0.0.x:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-0.0.5.tgz#96bb490bbd3ba6836bbfac111adf75301b1584de"
  dependencies:
    lru-cache "~1.0.2"

minimatch@0.3:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-0.3.0.tgz#275d8edaac4f1bb3326472089e7949c8394699dd"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

"minimatch@2 || 3", minimatch@^3.0.0, minimatch@^3.0.2, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@2.x, minimatch@^2.0.1:
  version "2.0.10"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-2.0.10.tgz#8d087c39c6b38c001b97fca7ce6d0e1e80afbac7"
  dependencies:
    brace-expansion "^1.0.0"

minimatch@~0.2.11, minimatch@~0.2.12, minimatch@~0.2.5:
  version "0.2.14"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-0.2.14.tgz#c74e780574f63c6f9a090e90efbe6ef53a6a756a"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

minimist@0.0.8:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@1.2.0, minimist@^1.1.3, minimist@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

minimist@~0.0.1:
  version "0.0.10"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"

minipass@^2.2.0, minipass@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/minipass/-/minipass-2.2.1.tgz#5ada97538b1027b4cf7213432428578cb564011f"
  dependencies:
    yallist "^3.0.0"

mkdirp@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.3.0.tgz#1bbf5ab1ba827af23575143490426455f481fe1e"

mkdirp@0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.0.tgz#1d73076a6df986cd9344e15e71fcc05a4c9abf12"
  dependencies:
    minimist "0.0.8"

mkdirp@0.5.1, mkdirp@0.5.x, "mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@~0.5.0:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

mocha@^2.3.3:
  version "2.5.3"
  resolved "https://registry.yarnpkg.com/mocha/-/mocha-2.5.3.tgz#161be5bdeb496771eb9b35745050b622b5aefc58"
  dependencies:
    commander "2.3.0"
    debug "2.2.0"
    diff "1.4.0"
    escape-string-regexp "1.0.2"
    glob "3.2.11"
    growl "1.9.2"
    jade "0.26.3"
    mkdirp "0.5.1"
    supports-color "1.2.0"
    to-iso-string "0.0.2"

modify-babel-preset@2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/modify-babel-preset/-/modify-babel-preset-2.0.2.tgz#bfa509669fe49f4222c0ce171ba44ed0e81551e7"
  dependencies:
    require-relative "^0.8.7"

ms@0.7.1:
  version "0.7.1"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.1.tgz#9cd13c03adbff25b65effde7ce864ee952017098"

ms@0.7.2:
  version "0.7.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.2.tgz#ae25cf2512b3885a1d95d7f037868d8431124765"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

mute-stream@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.5.tgz#8fbfabb0a98a253d3184331f9e8deb7372fac6c0"

nan@^2.3.0:
  version "2.9.2"
  resolved "https://registry.yarnpkg.com/nan/-/nan-2.9.2.tgz#f564d75f5f8f36a6d9456cca7a6c4fe488ab7866"

negotiator@0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.1.tgz#2b327184e8992101177b28563fb5e7102acd0ca9"

node-int64@~0.3.0:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/node-int64/-/node-int64-0.3.3.tgz#2d6e6b2ece5de8588b43d88d1bc41b26cd1fa84d"

node-libs-browser@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/node-libs-browser/-/node-libs-browser-0.7.0.tgz#3e272c0819e308935e26674408d7af0e1491b83b"
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.1.4"
    buffer "^4.9.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "3.3.0"
    domain-browser "^1.1.1"
    events "^1.0.0"
    https-browserify "0.0.1"
    os-browserify "^0.2.0"
    path-browserify "0.0.0"
    process "^0.11.0"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.0.5"
    stream-browserify "^2.0.1"
    stream-http "^2.3.1"
    string_decoder "^0.10.25"
    timers-browserify "^2.0.2"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.10.3"
    vm-browserify "0.0.4"

node-pre-gyp@^0.6.39:
  version "0.6.39"
  resolved "https://registry.yarnpkg.com/node-pre-gyp/-/node-pre-gyp-0.6.39.tgz#c00e96860b23c0e1420ac7befc5044e1d78d8649"
  dependencies:
    detect-libc "^1.0.2"
    hawk "3.1.3"
    mkdirp "^0.5.1"
    nopt "^4.0.1"
    npmlog "^4.0.2"
    rc "^1.1.7"
    request "2.81.0"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^2.2.1"
    tar-pack "^3.4.0"

node-uuid@~1.4.0:
  version "1.4.8"
  resolved "https://registry.yarnpkg.com/node-uuid/-/node-uuid-1.4.8.tgz#b040eb0923968afabf8d32fb1f17f1167fdab907"

nodeunit@~0.7.4:
  version "0.7.4"
  resolved "https://registry.yarnpkg.com/nodeunit/-/nodeunit-0.7.4.tgz#c908def7f299fbe65ff7ac888782955c46aae9f8"
  dependencies:
    tap ">=0.2.3"

nopt@3.x:
  version "3.0.6"
  resolved "https://registry.yarnpkg.com/nopt/-/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

nopt@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/nopt/-/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

nopt@~1.0.10:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/nopt/-/nopt-1.0.10.tgz#6ddd21bd2a31417b92727dd585f8a6f37608ebee"
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/normalize-package-data/-/normalize-package-data-2.4.0.tgz#12f95a307d58352075a04907b84ac8be98ac012f"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.0, normalize-path@^2.0.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  dependencies:
    remove-trailing-separator "^1.0.1"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  dependencies:
    path-key "^2.0.0"

npmlog@^4.0.2:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/npmlog/-/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

nyc@^11.3.0:
  version "11.4.1"
  resolved "https://registry.yarnpkg.com/nyc/-/nyc-11.4.1.tgz#13fdf7e7ef22d027c61d174758f6978a68f4f5e5"
  dependencies:
    archy "^1.0.0"
    arrify "^1.0.1"
    caching-transform "^1.0.0"
    convert-source-map "^1.3.0"
    debug-log "^1.0.1"
    default-require-extensions "^1.0.0"
    find-cache-dir "^0.1.1"
    find-up "^2.1.0"
    foreground-child "^1.5.3"
    glob "^7.0.6"
    istanbul-lib-coverage "^1.1.1"
    istanbul-lib-hook "^1.1.0"
    istanbul-lib-instrument "^1.9.1"
    istanbul-lib-report "^1.1.2"
    istanbul-lib-source-maps "^1.2.2"
    istanbul-reports "^1.1.3"
    md5-hex "^1.2.0"
    merge-source-map "^1.0.2"
    micromatch "^2.3.11"
    mkdirp "^0.5.0"
    resolve-from "^2.0.0"
    rimraf "^2.5.4"
    signal-exit "^3.0.1"
    spawn-wrap "^1.4.2"
    test-exclude "^4.1.1"
    yargs "^10.0.3"
    yargs-parser "^8.0.0"

oauth-sign@~0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/oauth-sign/-/oauth-sign-0.6.0.tgz#7dbeae44f6ca454e1f168451d630746735813ce3"

oauth-sign@~0.8.1, oauth-sign@~0.8.2:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/oauth-sign/-/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"

object-assign@4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.0.tgz#7a3b3d0e98063d43f4c03f2e8ae6cd51a86883a0"

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-component@0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/object-component/-/object-component-0.0.3.tgz#f0c69aa50efc95b866c186f400a33769cb2f1291"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/on-headers/-/on-headers-1.0.1.tgz#928f5d0f470d49342651ea6794b0857c100693f7"

once@1.x, once@^1.3.0, once@^1.3.3, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"

open@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/open/-/open-0.0.5.tgz#42c3e18ec95466b6bf0dc42f3a2945c3f0cad8fc"

opener@^1.4.1:
  version "1.4.3"
  resolved "https://registry.yarnpkg.com/opener/-/opener-1.4.3.tgz#5c6da2c5d7e5831e8ffa3964950f8d6674ac90b8"

optimist@^0.6.1, optimist@~0.6.0, optimist@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

optionator@^0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.5.0.tgz#b75a8995a2d417df25b6e4e3862f50aa88651368"
  dependencies:
    deep-is "~0.1.2"
    fast-levenshtein "~1.0.0"
    levn "~0.2.5"
    prelude-ls "~1.1.1"
    type-check "~0.3.1"
    wordwrap "~0.0.2"

optionator@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.6.0.tgz#b63ecbbf0e315fad4bc9827b45dc7ba45284fcb6"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~1.0.6"
    levn "~0.2.5"
    prelude-ls "~1.1.1"
    type-check "~0.3.1"
    wordwrap "~0.0.2"

options@>=0.0.5:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/options/-/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"

original@>=0.0.5:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/original/-/original-1.0.0.tgz#9147f93fa1696d04be61e01bd50baeaca656bd3b"
  dependencies:
    url-parse "1.0.x"

os-browserify@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/os-browserify/-/os-browserify-0.2.1.tgz#63fc4ccee5d2d7763d26bbf8601078e6c2e0044f"

os-homedir@^1.0.0, os-homedir@^1.0.1, os-homedir@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/os-locale/-/os-locale-2.1.0.tgz#42bc2900a6b5b8bd17376c8e882b65afccf24bf2"
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/osenv/-/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

own-or-env@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/own-or-env/-/own-or-env-1.0.1.tgz#54ce601d3bf78236c5c65633aa1c8ec03f8007e4"
  dependencies:
    own-or "^1.0.0"

own-or@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/own-or/-/own-or-1.0.0.tgz#4e877fbeda9a2ec8000fbc0bcae39645ee8bf8dc"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"

p-limit@^1.1.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-1.2.0.tgz#0e92b6bedcb59f022c13d0f1949dc82d15909f1c"
  dependencies:
    p-try "^1.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/p-try/-/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"

"package@>= 1.0.0 < 1.2.0":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/package/-/package-1.0.1.tgz#d25a1f99e2506dcb27d6704b83dca8a312e4edcc"

pako@~0.2.0:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/pako/-/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parsejson@0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/parsejson/-/parsejson-0.0.3.tgz#ab7e3759f209ece99437973f7d0f1f64ae0e64ab"
  dependencies:
    better-assert "~1.0.0"

parseqs@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/parseqs/-/parseqs-0.0.5.tgz#d5208a3738e46766e291ba2ea173684921a8b89d"
  dependencies:
    better-assert "~1.0.0"

parseuri@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/parseuri/-/parseuri-0.0.5.tgz#80204a50d4dbb779bfdc6ebe2778d90e4bce320a"
  dependencies:
    better-assert "~1.0.0"

parseurl@~1.3.0, parseurl@~1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.2.tgz#fc289d4ed8993119460c156253262cdc8de65bf3"

path-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/path-browserify/-/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-key@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

pause@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/pause/-/pause-0.0.1.tgz#1d408b3fdb76923b9543d96fb4c9dfd535d9cb5d"

pbkdf2-compat@2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pbkdf2-compat/-/pbkdf2-compat-2.0.1.tgz#b6e0c8fa99494d94e0511575802a59a5c142f288"

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/pend/-/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"

performance-now@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/performance-now/-/performance-now-0.2.0.tgz#33ef30c5c77d4ea21c5a53869d91b56d8f2555e5"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"

phantomjs-prebuilt@^2.1.5, phantomjs-prebuilt@^2.1.7:
  version "2.1.16"
  resolved "https://registry.yarnpkg.com/phantomjs-prebuilt/-/phantomjs-prebuilt-2.1.16.tgz#efd212a4a3966d3647684ea8ba788549be2aefef"
  dependencies:
    es6-promise "^4.0.3"
    extract-zip "^1.6.5"
    fs-extra "^1.0.0"
    hasha "^2.2.0"
    kew "^0.7.0"
    progress "^1.1.8"
    request "^2.81.0"
    request-progress "^2.0.1"
    which "^1.2.10"

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  dependencies:
    find-up "^1.0.0"

pkginfo@0.2.x:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/pkginfo/-/pkginfo-0.2.3.tgz#7239c42a5ef6c30b8f328439d9b9ff71042490f8"

pkginfo@0.x.x:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/pkginfo/-/pkginfo-0.4.1.tgz#b5418ef0439de5425fc4995042dced14fb2a84ff"

prelude-ls@~1.1.0, prelude-ls@~1.1.1, prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

pretty-bytes@^1.0.0:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/pretty-bytes/-/pretty-bytes-1.0.4.tgz#0a22e8210609ad35542f8c8d5d2159aff0751c84"
  dependencies:
    get-stdin "^4.0.1"
    meow "^3.1.0"

private@^0.1.6, private@^0.1.7, private@~0.1.5:
  version "0.1.8"
  resolved "https://registry.yarnpkg.com/private/-/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"

process-nextick-args@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"

process@^0.11.0:
  version "0.11.10"
  resolved "https://registry.yarnpkg.com/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

progress@^1.1.8:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/progress/-/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

prompt@~0.1.12:
  version "0.1.12"
  resolved "https://registry.yarnpkg.com/prompt/-/prompt-0.1.12.tgz#d3114e4fb985ac66eaa35586dcb7b3fb3b27bfc6"
  dependencies:
    async "0.1.x"
    colors "0.x.x"
    pkginfo "0.x.x"
    winston "0.5.x"

proxy-addr@~2.0.2:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/proxy-addr/-/proxy-addr-2.0.3.tgz#355f262505a621646b3130a728eb647e22055341"
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.6.0"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.2.4, punycode@^1.3.2, punycode@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

q@^1.4.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/q/-/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"

q@~1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/q/-/q-1.4.1.tgz#55705bcd93c5f3673530c2c2cbc0c2b3addc286e"

qs@0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/qs/-/qs-0.5.1.tgz#9f6bf5d9ac6c76384e95d36d15b48980e5e4add0"

qs@5.2.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-5.2.0.tgz#a9f31142af468cb72b25b30136ba2456834916be"

qs@6.5.1, qs@~6.5.1:
  version "6.5.1"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.5.1.tgz#349cdf6eef89ec45c12d7d5eb3fc0c870343a6d8"

qs@~2.4.0:
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/qs/-/qs-2.4.2.tgz#f7ce788e5777df0b5010da7f7c4e73ba32470f5a"

qs@~5.1.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-5.1.0.tgz#4d932e5c7ea411cca76a312d39a606200fd50cd9"

qs@~6.3.0:
  version "6.3.2"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.3.2.tgz#e75bd5f6e268122a2a0e0bda630b2550c166502c"

qs@~6.4.0:
  version "6.4.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.4.0.tgz#13e26d28ad6b0ffaa91312cd3bf708ed351e7233"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

querystringify@0.0.x:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/querystringify/-/querystringify-0.0.4.tgz#0cf7f84f9463ff0ae51c4c4b142d95be37724d9c"

querystringify@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/querystringify/-/querystringify-1.0.0.tgz#6286242112c5b712fa654e526652bf6a13ff05cb"

randomatic@^1.1.3:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/randomatic/-/randomatic-1.1.7.tgz#c7abe9cc8b87c0baa876b19fde83fd464797e38c"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

range-parser@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-0.0.4.tgz#c0427ffef51c10acba0782a46c9602e744ff620b"

range-parser@^1.0.3, range-parser@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.2.0.tgz#f49be6b487894ddc40dcc94a322f611092e00d5e"

raw-body@2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/raw-body/-/raw-body-2.3.2.tgz#bcd60c77d3eb93cde0050295c3f379389bc88f89"
  dependencies:
    bytes "3.0.0"
    http-errors "1.6.2"
    iconv-lite "0.4.19"
    unpipe "1.0.0"

raw-body@~2.1.5:
  version "2.1.7"
  resolved "https://registry.yarnpkg.com/raw-body/-/raw-body-2.1.7.tgz#adfeace2e4fb3098058014d08c072dcc59758774"
  dependencies:
    bytes "2.4.0"
    iconv-lite "0.4.13"
    unpipe "1.0.0"

rc@^1.1.7:
  version "1.2.5"
  resolved "https://registry.yarnpkg.com/rc/-/rc-1.2.5.tgz#275cd687f6e3b36cc756baa26dfee80a790301fd"
  dependencies:
    deep-extend "~0.4.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

readable-stream@^2, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.0.6, readable-stream@^2.1.4, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3:
  version "2.3.5"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.5.tgz#b4f85003a938cbb6ecbce2a124fb1012bd1a838d"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.0.3"
    util-deprecate "~1.0.1"

readable-stream@~1.0.2, readable-stream@~1.0.24, readable-stream@~1.0.26, readable-stream@~1.0.33:
  version "1.0.34"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/readdirp/-/readdirp-2.1.0.tgz#4ed0ad060df3073300c48440373f72d1cc642d78"
  dependencies:
    graceful-fs "^4.1.2"
    minimatch "^3.0.2"
    readable-stream "^2.0.2"
    set-immediate-shim "^1.0.1"

readline2@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/readline2/-/readline2-1.0.1.tgz#41059608ffc154757b715d9989d199ffbf372e35"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    mute-stream "0.0.5"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/redent/-/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

regenerate@^1.2.1:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/regenerate/-/regenerate-1.3.3.tgz#0c336d3980553d755c39b586ae3b20aa49c82b7f"

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://registry.yarnpkg.com/regenerator-transform/-/regenerator-transform-0.10.1.tgz#1e4996837231da8b7f3cf4114d71b5691a0680dd"
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "https://registry.yarnpkg.com/regex-cache/-/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  dependencies:
    is-equal-shallow "^0.1.3"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/regexpu-core/-/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/regjsgen/-/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/regjsparser/-/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"

repeat-element@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/repeat-element/-/repeat-element-1.1.2.tgz#ef089a178d1483baae4d93eb98b4f9e4e11d990a"

repeat-string@^0.2.2:
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-0.2.2.tgz#c7a8d3236068362059a7e4651fc6884e8b1fb4ae"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

request-progress@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/request-progress/-/request-progress-2.0.1.tgz#5d36bb57961c673aa5b788dbc8141fdf23b44e08"
  dependencies:
    throttleit "^1.0.0"

request@2.79.0:
  version "2.79.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.79.0.tgz#4dfe5bf6be8b8cdc37fcf93e04b65577722710de"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.11.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~2.0.6"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    oauth-sign "~0.8.1"
    qs "~6.3.0"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "~0.4.1"
    uuid "^3.0.0"

request@2.81.0:
  version "2.81.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.81.0.tgz#c6928946a0e06c5f8d6f8a9333469ffda46298a0"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.12.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~4.2.1"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    oauth-sign "~0.8.1"
    performance-now "^0.2.0"
    qs "~6.4.0"
    safe-buffer "^5.0.1"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "^0.6.0"
    uuid "^3.0.0"

request@2.9.x:
  version "2.9.203"
  resolved "https://registry.yarnpkg.com/request/-/request-2.9.203.tgz#6c1711a5407fb94a114219563e44145bcbf4723a"

request@^2.81.0:
  version "2.83.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.83.0.tgz#ca0b65da02ed62935887808e6f510381034e3356"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.6.0"
    caseless "~0.12.0"
    combined-stream "~1.0.5"
    extend "~3.0.1"
    forever-agent "~0.6.1"
    form-data "~2.3.1"
    har-validator "~5.0.3"
    hawk "~6.0.2"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.17"
    oauth-sign "~0.8.2"
    performance-now "^2.1.0"
    qs "~6.5.1"
    safe-buffer "^5.1.1"
    stringstream "~0.0.5"
    tough-cookie "~2.3.3"
    tunnel-agent "^0.6.0"
    uuid "^3.1.0"

request@~2.55.0:
  version "2.55.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.55.0.tgz#d75c1cdf679d76bb100f9bffe1fe551b5c24e93d"
  dependencies:
    aws-sign2 "~0.5.0"
    bl "~0.9.0"
    caseless "~0.9.0"
    combined-stream "~0.0.5"
    forever-agent "~0.6.0"
    form-data "~0.2.0"
    har-validator "^1.4.0"
    hawk "~2.3.0"
    http-signature "~0.10.0"
    isstream "~0.1.1"
    json-stringify-safe "~5.0.0"
    mime-types "~2.0.1"
    node-uuid "~1.4.0"
    oauth-sign "~0.6.0"
    qs "~2.4.0"
    stringstream "~0.0.4"
    tough-cookie ">=0.12.0"
    tunnel-agent "~0.4.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

require-relative@^0.8.7:
  version "0.8.7"
  resolved "https://registry.yarnpkg.com/require-relative/-/require-relative-0.8.7.tgz#7999539fc9e047a37928fa196f8e1563dabd36de"

requires-port@1.0.x, requires-port@1.x.x, requires-port@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"

resolve-from@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-2.0.0.tgz#9480ab20e94ffa1d9e80a804c7ea147611966b57"

resolve@1.1.x:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"

resolve@~0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-0.3.1.tgz#34c63447c664c70598d1c9b126fc43b2a24310a4"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  dependencies:
    align-text "^0.1.1"

rimraf@2, rimraf@^2.2.8, rimraf@^2.3.3, rimraf@^2.5.1, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.2:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
  dependencies:
    glob "^7.0.5"

rimraf@2.4.3:
  version "2.4.3"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.4.3.tgz#e5b51c9437a4c582adb955e9f28cf8d945e272af"
  dependencies:
    glob "^5.0.14"

rimraf@~2.2.8:
  version "2.2.8"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"

ripemd160@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/ripemd160/-/ripemd160-0.2.0.tgz#2bf198bde167cacfa51c0a928e84b68bbe171fce"

run-async@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/run-async/-/run-async-0.1.0.tgz#c8ad4a5e110661e402a7d21b530e009f25f8e389"
  dependencies:
    once "^1.3.0"

rx-lite@^3.1.2:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-3.1.2.tgz#19ce502ca572665f3b647b10939f97fd1615f102"

safe-buffer@5.1.1, safe-buffer@^5.0.1, safe-buffer@^5.1.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.1.tgz#893312af69b2123def71f57889001671eeb2c853"

sauce-connect-launcher@^0.13.0:
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/sauce-connect-launcher/-/sauce-connect-launcher-0.13.0.tgz#25d7df9da16a5ed1caa13df424cb57cb0b6d5a22"
  dependencies:
    adm-zip "~0.4.3"
    async "1.4.0"
    lodash "3.10.1"
    rimraf "2.4.3"

saucelabs@^1.0.1:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/saucelabs/-/saucelabs-1.4.0.tgz#b934a9af9da2874b3f40aae1fcde50a4466f5f38"
  dependencies:
    https-proxy-agent "^1.0.0"

"semver@2 || 3 || 4 || 5", semver@^5.0.3, semver@^5.3.0:
  version "5.5.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.5.0.tgz#dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab"

semver@~1.0.13:
  version "1.0.14"
  resolved "https://registry.yarnpkg.com/semver/-/semver-1.0.14.tgz#cac5e2d55a6fbf958cb220ae844045071c78f676"

semver@~4.3.3:
  version "4.3.6"
  resolved "https://registry.yarnpkg.com/semver/-/semver-4.3.6.tgz#300bc6e0e86374f7ba61068b5b1ecd57fc6532da"

semver@~5.0.1:
  version "5.0.3"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.0.3.tgz#77466de589cd5d3c95f138aa78bc569a3cb5d27a"

send@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/send/-/send-0.0.4.tgz#2d4cf79b189fcd09610e1302510ac9b0e4dde800"
  dependencies:
    debug "*"
    fresh "0.1.0"
    mime "1.2.6"
    range-parser "0.0.4"

send@0.16.1:
  version "0.16.1"
  resolved "https://registry.yarnpkg.com/send/-/send-0.16.1.tgz#a70e1ca21d1382c11d0d9f6231deb281080d7ab3"
  dependencies:
    debug "2.6.9"
    depd "~1.1.1"
    destroy "~1.0.4"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.6.2"
    mime "1.4.1"
    ms "2.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.3.1"

serve-index@^1.7.2:
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/serve-index/-/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.13.1:
  version "1.13.1"
  resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.13.1.tgz#4c57d53404a761d8f2e7c1e8a18a47dbf278a719"
  dependencies:
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    parseurl "~1.3.2"
    send "0.16.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-immediate-shim@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz#4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"

setprototypeof@1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.0.3.tgz#66567e37043eeb4f04d91bd658c0cbefb55b8e04"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"

sha.js@2.2.6:
  version "2.2.6"
  resolved "https://registry.yarnpkg.com/sha.js/-/sha.js-2.2.6.tgz#17ddeddc5f722fb66501658895461977867315ba"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"

shelljs@^0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/shelljs/-/shelljs-0.5.3.tgz#c54982b996c76ef0c1e6b59fbdc5825f5b713113"

sigmund@~1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/sigmund/-/sigmund-1.0.1.tgz#3ff21f198cad2175f9f3b781853fd94d0d19b590"

signal-exit@^3.0.0, signal-exit@^3.0.1, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

slide@^1.1.5:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/slide/-/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"

sntp@1.x.x:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/sntp/-/sntp-1.0.9.tgz#6541184cc90aeea6c6e7b35e2659082443c66198"
  dependencies:
    hoek "2.x.x"

sntp@2.x.x:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/sntp/-/sntp-2.1.0.tgz#2c6cec14fedc2222739caf9b5c3d85d1cc5a2cc8"
  dependencies:
    hoek "4.x.x"

socket.io-adapter@0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/socket.io-adapter/-/socket.io-adapter-0.5.0.tgz#cb6d4bb8bec81e1078b99677f9ced0046066bb8b"
  dependencies:
    debug "2.3.3"
    socket.io-parser "2.3.1"

socket.io-client@1.7.4:
  version "1.7.4"
  resolved "https://registry.yarnpkg.com/socket.io-client/-/socket.io-client-1.7.4.tgz#ec9f820356ed99ef6d357f0756d648717bdd4281"
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "1.2.1"
    debug "2.3.3"
    engine.io-client "~1.8.4"
    has-binary "0.1.7"
    indexof "0.0.1"
    object-component "0.0.3"
    parseuri "0.0.5"
    socket.io-parser "2.3.1"
    to-array "0.1.4"

socket.io-parser@2.3.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/socket.io-parser/-/socket.io-parser-2.3.1.tgz#dd532025103ce429697326befd64005fcfe5b4a0"
  dependencies:
    component-emitter "1.1.2"
    debug "2.2.0"
    isarray "0.0.1"
    json3 "3.3.2"

socket.io@^1.4.5:
  version "1.7.4"
  resolved "https://registry.yarnpkg.com/socket.io/-/socket.io-1.7.4.tgz#2f7ecedc3391bf2d5c73e291fe233e6e34d4dd00"
  dependencies:
    debug "2.3.3"
    engine.io "~1.8.4"
    has-binary "0.1.7"
    object-assign "4.1.0"
    socket.io-adapter "0.5.0"
    socket.io-client "1.7.4"
    socket.io-parser "2.3.1"

sockjs-client@^1.0.3:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/sockjs-client/-/sockjs-client-1.1.4.tgz#5babe386b775e4cf14e7520911452654016c8b12"
  dependencies:
    debug "^2.6.6"
    eventsource "0.1.6"
    faye-websocket "~0.11.0"
    inherits "^2.0.1"
    json3 "^3.3.2"
    url-parse "^1.1.8"

sockjs@^0.3.15:
  version "0.3.19"
  resolved "https://registry.yarnpkg.com/sockjs/-/sockjs-0.3.19.tgz#d976bbe800af7bd20ae08598d582393508993c0d"
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

source-list-map@~0.1.7:
  version "0.1.8"
  resolved "https://registry.yarnpkg.com/source-list-map/-/source-list-map-0.1.8.tgz#c550b2ab5427f6b3f21f5afead88c4f5587b2106"

source-map-support@^0.3.2:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.3.3.tgz#34900977d5ba3f07c7757ee72e73bb1a9b53754f"
  dependencies:
    source-map "0.1.32"

source-map-support@^0.4.15, source-map-support@^0.4.18:
  version "0.4.18"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.4.18.tgz#0286a6de8be42641338594e97ccea75f0a2c585f"
  dependencies:
    source-map "^0.5.6"

source-map@0.1.32:
  version "0.1.32"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.1.32.tgz#c8b6c167797ba4740a8ea33252162ff08591b266"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.1.41:
  version "0.1.43"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.1.43.tgz#c24bc146ca517c1471f5dacbe2571b2b7f9e3346"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.4.4, source-map@~0.4.1:
  version "0.4.4"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7, source-map@~0.5.1:
  version "0.5.7"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"

source-map@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.2.0.tgz#dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d"
  dependencies:
    amdefine ">=0.0.4"

spawn-wrap@^1.4.2:
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/spawn-wrap/-/spawn-wrap-1.4.2.tgz#cff58e73a8224617b6561abdc32586ea0c82248c"
  dependencies:
    foreground-child "^1.5.6"
    mkdirp "^0.5.0"
    os-homedir "^1.0.1"
    rimraf "^2.6.2"
    signal-exit "^3.0.2"
    which "^1.3.0"

spdx-correct@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/spdx-correct/-/spdx-correct-3.0.0.tgz#05a5b4d7153a195bc92c3c425b69f3b2a9524c82"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/spdx-exceptions/-/spdx-exceptions-2.1.0.tgz#2c7ae61056c714a5b9b9b2b2af7d311ef5c78fe9"

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/spdx-expression-parse/-/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/spdx-license-ids/-/spdx-license-ids-3.0.0.tgz#7a7cd28470cc6d3a1cfe6d66886f6bc430d3ac87"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.13.1"
  resolved "https://registry.yarnpkg.com/sshpk/-/sshpk-1.13.1.tgz#512df6da6287144316dc4c18fe1cf1d940739be3"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    dashdash "^1.12.0"
    getpass "^0.1.1"
  optionalDependencies:
    bcrypt-pbkdf "^1.0.0"
    ecc-jsbn "~0.1.1"
    jsbn "~0.1.0"
    tweetnacl "~0.14.0"

stack-trace@0.0.x:
  version "0.0.10"
  resolved "https://registry.yarnpkg.com/stack-trace/-/stack-trace-0.0.10.tgz#547c70b347e8d32b4e108ea1a2a159e5fdde19c0"

stack-utils@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/stack-utils/-/stack-utils-1.0.1.tgz#d4f33ab54e8e38778b0ca5cfd3b3afb12db68620"

statuses@1, "statuses@>= 1.3.1 < 2":
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.4.0.tgz#bb73d446da2796106efcc1b601a253d6c46bd087"

statuses@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.3.1.tgz#faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e"

stream-browserify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-2.0.1.tgz#66266ee5f9bdb9940a4e4514cafb43bb71e5c9db"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-cache@~0.0.1:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/stream-cache/-/stream-cache-0.0.2.tgz#1ac5ad6832428ca55667dbdee395dad4e6db118f"

stream-http@^2.3.1:
  version "2.8.0"
  resolved "https://registry.yarnpkg.com/stream-http/-/stream-http-2.8.0.tgz#fd86546dac9b1c91aff8fc5d287b98fafb41bc10"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.3"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string_decoder@^0.10.25, string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.0.3.tgz#0fc67d7c141825de94282dd536bec6b9bce860ab"
  dependencies:
    safe-buffer "~5.1.0"

stringstream@~0.0.4, stringstream@~0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/stringstream/-/stringstream-0.0.5.tgz#4e484cd4de5a0bbbee18e46307710a8a81621878"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/strip-indent/-/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@~1.0.1:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-1.0.4.tgz#1e15fbcac97d3ee99bf2d73b4c656b082bbafb91"

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

supports-color@1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-1.2.0.tgz#ff1ed1e61169d06b3cf2d588e188b18d8847e17e"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^3.1.0, supports-color@^3.1.1, supports-color@^3.1.2:
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0:
  version "5.3.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-5.3.0.tgz#5b24ac15db80fa927cf5227a4a33fd3c4c7676c0"
  dependencies:
    has-flag "^3.0.0"

tap-mocha-reporter@^3.0.6:
  version "3.0.6"
  resolved "https://registry.yarnpkg.com/tap-mocha-reporter/-/tap-mocha-reporter-3.0.6.tgz#12abe97ff409a5a6ecc3d70b6dba34d82184a770"
  dependencies:
    color-support "^1.1.0"
    debug "^2.1.3"
    diff "^1.3.2"
    escape-string-regexp "^1.0.3"
    glob "^7.0.5"
    js-yaml "^3.3.1"
    tap-parser "^5.1.0"
    unicode-length "^1.0.0"
  optionalDependencies:
    readable-stream "^2.1.5"

tap-parser@^5.1.0:
  version "5.4.0"
  resolved "https://registry.yarnpkg.com/tap-parser/-/tap-parser-5.4.0.tgz#6907e89725d7b7fa6ae41ee2c464c3db43188aec"
  dependencies:
    events-to-array "^1.0.1"
    js-yaml "^3.2.7"
  optionalDependencies:
    readable-stream "^2"

tap-parser@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/tap-parser/-/tap-parser-7.0.0.tgz#54db35302fda2c2ccc21954ad3be22b2cba42721"
  dependencies:
    events-to-array "^1.0.1"
    js-yaml "^3.2.7"
    minipass "^2.2.0"

tap@>=0.2.3:
  version "11.1.1"
  resolved "https://registry.yarnpkg.com/tap/-/tap-11.1.1.tgz#6dbd23c487127f621a95c793f7a247fa7e2c053a"
  dependencies:
    bind-obj-methods "^1.0.0"
    bluebird "^3.5.1"
    clean-yaml-object "^0.1.0"
    color-support "^1.1.0"
    coveralls "^2.13.3"
    foreground-child "^1.3.3"
    fs-exists-cached "^1.0.0"
    function-loop "^1.0.1"
    glob "^7.0.0"
    isexe "^2.0.0"
    js-yaml "^3.10.0"
    minipass "^2.2.1"
    mkdirp "^0.5.1"
    nyc "^11.3.0"
    opener "^1.4.1"
    os-homedir "^1.0.2"
    own-or "^1.0.0"
    own-or-env "^1.0.0"
    rimraf "^2.6.2"
    signal-exit "^3.0.0"
    source-map-support "^0.4.18"
    stack-utils "^1.0.0"
    tap-mocha-reporter "^3.0.6"
    tap-parser "^7.0.0"
    tmatch "^3.1.0"
    trivial-deferred "^1.0.1"
    tsame "^1.1.2"
    write-file-atomic "^2.3.0"
    yapool "^1.0.0"

tapable@^0.1.8, tapable@~0.1.8:
  version "0.1.10"
  resolved "https://registry.yarnpkg.com/tapable/-/tapable-0.1.10.tgz#29c35707c2b70e50d07482b5d202e8ed446dafd4"

tar-pack@^3.4.0:
  version "3.4.1"
  resolved "https://registry.yarnpkg.com/tar-pack/-/tar-pack-3.4.1.tgz#e1dbc03a9b9d3ba07e896ad027317eb679a10a1f"
  dependencies:
    debug "^2.2.0"
    fstream "^1.0.10"
    fstream-ignore "^1.0.5"
    once "^1.3.3"
    readable-stream "^2.1.4"
    rimraf "^2.5.1"
    tar "^2.2.1"
    uid-number "^0.0.6"

tar-stream@~1.1.0:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/tar-stream/-/tar-stream-1.1.5.tgz#be9218c130c20029e107b0f967fb23de0579d13c"
  dependencies:
    bl "^0.9.0"
    end-of-stream "^1.0.0"
    readable-stream "~1.0.33"
    xtend "^4.0.0"

tar@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/tar/-/tar-2.2.1.tgz#8e4d2a256c0e2185c6b18ad694aec968b83cb1d1"
  dependencies:
    block-stream "*"
    fstream "^1.0.2"
    inherits "2"

temporary@~0.0.4:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/temporary/-/temporary-0.0.8.tgz#a18a981d28ba8ca36027fb3c30538c3ecb740ac0"
  dependencies:
    package ">= 1.0.0 < 1.2.0"

test-exclude@^4.1.1:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/test-exclude/-/test-exclude-4.2.0.tgz#07e3613609a362c74516a717515e13322ab45b3c"
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"

throttleit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/throttleit/-/throttleit-1.0.0.tgz#9e785836daf46743145a5984b6268d828528ac6c"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

time-stamp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/time-stamp/-/time-stamp-2.0.0.tgz#95c6a44530e15ba8d6f4a3ecb8c3a3fac46da357"

timers-browserify@^2.0.2:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-2.0.6.tgz#241e76927d9ca05f4d959819022f5b3664b64bae"
  dependencies:
    setimmediate "^1.0.4"

timespan@2.x.x:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/timespan/-/timespan-2.3.0.tgz#4902ce040bd13d845c8f59b27e9d59bad6f39929"

tiny-lr@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/tiny-lr/-/tiny-lr-0.2.1.tgz#b3fdba802e5d56a33c2f6f10794b32e477ac729d"
  dependencies:
    body-parser "~1.14.0"
    debug "~2.2.0"
    faye-websocket "~0.10.0"
    livereload-js "^2.2.0"
    parseurl "~1.3.0"
    qs "~5.1.0"

tmatch@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/tmatch/-/tmatch-3.1.0.tgz#701264fd7582d0144a80c85af3358cca269c71e3"

tmp@0.0.x:
  version "0.0.33"
  resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  dependencies:
    os-tmpdir "~1.0.2"

to-array@0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/to-array/-/to-array-0.1.4.tgz#17e6c11f73dd4f3d74cda7a4ff3238e9ad9bf890"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

to-iso-string@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/to-iso-string/-/to-iso-string-0.0.2.tgz#4dc19e664dfccbe25bd8db508b00c6da158255d1"

tough-cookie@>=0.12.0, tough-cookie@~2.3.0, tough-cookie@~2.3.3:
  version "2.3.4"
  resolved "https://registry.yarnpkg.com/tough-cookie/-/tough-cookie-2.3.4.tgz#ec60cee38ac675063ffc97a5c18970578ee83655"
  dependencies:
    punycode "^1.4.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/trim-newlines/-/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/trim-right/-/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

trivial-deferred@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/trivial-deferred/-/trivial-deferred-1.0.1.tgz#376d4d29d951d6368a6f7a0ae85c2f4d5e0658f3"

tsame@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/tsame/-/tsame-1.1.2.tgz#5ce0002acf685942789c63018797a2aa5e6b03c5"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tunnel-agent@~0.4.0, tunnel-agent@~0.4.1:
  version "0.4.3"
  resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.4.3.tgz#6373db76909fe570e08d73583365ed828a74eeeb"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.yarnpkg.com/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-check@~0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  dependencies:
    prelude-ls "~1.1.2"

type-detect@0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/type-detect/-/type-detect-0.1.1.tgz#0ba5ec2a885640e470ea4e8505971900dac58822"

type-detect@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/type-detect/-/type-detect-1.0.0.tgz#762217cc06db258ec48908a1298e8b95121e8ea2"

type-is@~1.6.10, type-is@~1.6.15:
  version "1.6.16"
  resolved "https://registry.yarnpkg.com/type-is/-/type-is-1.6.16.tgz#f89ce341541c672b25ee7ae3c73dee3b2be50194"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.18"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

uglify-js@^2.6:
  version "2.8.29"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@~1.3.3:
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-1.3.5.tgz#4b5bfff9186effbaa888e4c9e94bd9fc4c94929d"

uglify-js@~2.6.2:
  version "2.6.4"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.6.4.tgz#65ea2fb3059c9394692f15fed87c2b36c16b9adf"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-js@~2.7.3:
  version "2.7.5"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.7.5.tgz#4612c0c7baaee2ba7c487de4904ae122079f2ca8"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

uid-number@^0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/uid-number/-/uid-number-0.0.6.tgz#0ea10e8035e8eb5b8e4449f06da1c730663baa81"

ultron@1.0.x:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/ultron/-/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"

underscore.string@~2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/underscore.string/-/underscore.string-2.1.1.tgz#458397799114b9b67f6030bb527b0afae689c061"

underscore.string@~2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/underscore.string/-/underscore.string-2.2.1.tgz#d7c0fa2af5d5a1a67f4253daee98132e733f0f19"

underscore.string@~2.3.3:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/underscore.string/-/underscore.string-2.3.3.tgz#71c08bf6b428b1133f37e78fa3a21c82f7329b0d"

underscore.string@~2.4.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/underscore.string/-/underscore.string-2.4.0.tgz#8cdd8fbac4e2d2ea1e7e2e8097c42f442280f85b"

underscore.string@~3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/underscore.string/-/underscore.string-3.0.3.tgz#4617b8c1a250cf6e5064fbbb363d0fa96cf14552"

underscore@~1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/underscore/-/underscore-1.2.4.tgz#e8da6241aa06f64df2473bb2590b8c17c84c3c7e"

underscore@~1.7.0:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/underscore/-/underscore-1.7.0.tgz#6bbaf0877500d36be34ecaa584e0db9fef035209"

unicode-length@^1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/unicode-length/-/unicode-length-1.0.3.tgz#5ada7a7fed51841a418a328cf149478ac8358abb"
  dependencies:
    punycode "^1.3.2"
    strip-ansi "^3.0.1"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

uri-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/uri-path/-/uri-path-1.0.0.tgz#9747f018358933c31de0fccfd82d138e67262e32"

url-parse@1.0.x:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/url-parse/-/url-parse-1.0.5.tgz#0854860422afdcfefeb6c965c662d4800169927b"
  dependencies:
    querystringify "0.0.x"
    requires-port "1.0.x"

url-parse@^1.1.8:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/url-parse/-/url-parse-1.2.0.tgz#3a19e8aaa6d023ddd27dcc44cb4fc8f7fec23986"
  dependencies:
    querystringify "~1.0.0"
    requires-port "~1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.yarnpkg.com/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

user-home@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/user-home/-/user-home-2.0.0.tgz#9c70bfd8169bc1dcbf48604e0f04b8b49cde9e9f"
  dependencies:
    os-homedir "^1.0.0"

useragent@^2.1.6:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/useragent/-/useragent-2.3.0.tgz#217f943ad540cb2128658ab23fc960f6a88c9972"
  dependencies:
    lru-cache "4.1.x"
    tmp "0.0.x"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util@0.10.3, util@^0.10.3:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"

uuid@^3.0.0, uuid@^3.0.1, uuid@^3.1.0:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.2.1.tgz#12c528bb9d58d0b9265d9a2f6f0fe8be17ff1f14"

validate-npm-package-license@^3.0.1:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/validate-npm-package-license/-/validate-npm-package-license-3.0.3.tgz#81643bcbef1bdfecd4623793dc4648948ba98338"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vargs@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/vargs/-/vargs-0.1.0.tgz#6b6184da6520cc3204ce1b407cac26d92609ebff"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/vm-browserify/-/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  dependencies:
    indexof "0.0.1"

void-elements@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/void-elements/-/void-elements-2.0.1.tgz#c066afb582bb1cb4128d60ea92392e94d5e9dbec"

watchpack@^0.2.1:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/watchpack/-/watchpack-0.2.9.tgz#62eaa4ab5e5ba35fdfc018275626e3c0f5e3fb0b"
  dependencies:
    async "^0.9.0"
    chokidar "^1.0.0"
    graceful-fs "^4.1.2"

wd@^0.3.4:
  version "0.3.12"
  resolved "https://registry.yarnpkg.com/wd/-/wd-0.3.12.tgz#3fb4f1d759f8c85dde5393d17334ffe03e9bb329"
  dependencies:
    archiver "~0.14.0"
    async "~1.0.0"
    lodash "~3.9.3"
    q "~1.4.1"
    request "~2.55.0"
    underscore.string "~3.0.3"
    vargs "~0.1.0"

webpack-core@~0.6.9:
  version "0.6.9"
  resolved "https://registry.yarnpkg.com/webpack-core/-/webpack-core-0.6.9.tgz#fc571588c8558da77be9efb6debdc5a3b172bdc2"
  dependencies:
    source-list-map "~0.1.7"
    source-map "~0.4.1"

webpack-dev-middleware@^1.0.11, webpack-dev-middleware@^1.10.2:
  version "1.12.2"
  resolved "https://registry.yarnpkg.com/webpack-dev-middleware/-/webpack-dev-middleware-1.12.2.tgz#f8fc1120ce3b4fc5680ceecb43d777966b21105e"
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.5.0"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"
    time-stamp "^2.0.0"

webpack-dev-server@^1.12.0:
  version "1.16.5"
  resolved "https://registry.yarnpkg.com/webpack-dev-server/-/webpack-dev-server-1.16.5.tgz#0cbd5f2d2ac8d4e593aacd5c9702e7bbd5e59892"
  dependencies:
    compression "^1.5.2"
    connect-history-api-fallback "^1.3.0"
    express "^4.13.3"
    http-proxy-middleware "~0.17.1"
    open "0.0.5"
    optimist "~0.6.1"
    serve-index "^1.7.2"
    sockjs "^0.3.15"
    sockjs-client "^1.0.3"
    stream-cache "~0.0.1"
    strip-ansi "^3.0.0"
    supports-color "^3.1.1"
    webpack-dev-middleware "^1.10.2"

webpack@^1.12.2:
  version "1.15.0"
  resolved "https://registry.yarnpkg.com/webpack/-/webpack-1.15.0.tgz#4ff31f53db03339e55164a9d468ee0324968fe98"
  dependencies:
    acorn "^3.0.0"
    async "^1.3.0"
    clone "^1.0.2"
    enhanced-resolve "~0.9.0"
    interpret "^0.6.4"
    loader-utils "^0.2.11"
    memory-fs "~0.3.0"
    mkdirp "~0.5.0"
    node-libs-browser "^0.7.0"
    optimist "~0.6.0"
    supports-color "^3.1.0"
    tapable "~0.1.8"
    uglify-js "~2.7.3"
    watchpack "^0.2.1"
    webpack-core "~0.6.9"

websocket-driver@>=0.5.1:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/websocket-driver/-/websocket-driver-0.7.0.tgz#0caf9d2d755d93aee049d4bdd0d3fe2cca2a24eb"
  dependencies:
    http-parser-js ">=0.4.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/websocket-extensions/-/websocket-extensions-0.1.3.tgz#5d2ff22977003ec687a4b87073dfbbac146ccf29"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/which-module/-/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"

which@^1.1.1, which@^1.2.10, which@^1.2.9, which@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/which/-/which-1.3.0.tgz#ff04bdfc010ee547d780bec38e1ac1c2777d253a"
  dependencies:
    isexe "^2.0.0"

which@~1.0.5:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/which/-/which-1.0.9.tgz#460c1da0f810103d0321a9b633af9e575e64486f"

wide-align@^1.1.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/wide-align/-/wide-align-1.1.2.tgz#571e0f1b0604636ebc0dfc21b0339bbe31341710"
  dependencies:
    string-width "^1.0.2"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

winston@0.5.x:
  version "0.5.11"
  resolved "https://registry.yarnpkg.com/winston/-/winston-0.5.11.tgz#9d84ead981a497a92ddf76616137abef661c414f"
  dependencies:
    async "0.1.x"
    colors "0.x.x"
    eyes "0.1.x"
    loggly "0.3.x >=0.3.7"
    pkginfo "0.2.x"
    stack-trace "0.0.x"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write-file-atomic@^1.1.4:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/write-file-atomic/-/write-file-atomic-1.3.4.tgz#f807a4f0b1d9e913ae7a48112e6cc3af1991b45f"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    slide "^1.1.5"

write-file-atomic@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/write-file-atomic/-/write-file-atomic-2.3.0.tgz#1ff61575c2e2a4e8e510d6fa4e243cce183999ab"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/write/-/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
  dependencies:
    mkdirp "^0.5.1"

ws@~1.1.5:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/ws/-/ws-1.1.5.tgz#cbd9e6e75e09fc5d2c90015f21f0c40875e0dd51"
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

wtf-8@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/wtf-8/-/wtf-8-1.0.0.tgz#392d8ba2d0f1c34d1ee2d630f15d0efb68e1048a"

xml-escape@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/xml-escape/-/xml-escape-1.0.0.tgz#00963d697b2adf0c185c4e04e73174ba9b288eb2"

xmlhttprequest-ssl@1.5.3:
  version "1.5.3"
  resolved "https://registry.yarnpkg.com/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.5.3.tgz#185a888c04eca46c3e4070d99f7b49de3528992d"

xtend@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"

yallist@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-3.0.2.tgz#8452b4bb7e83c7c188d8041c1a837c773d6d8bb9"

yapool@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/yapool/-/yapool-1.0.0.tgz#f693f29a315b50d9a9da2646a7a6645c96985b6a"

yargs-parser@^8.0.0, yargs-parser@^8.1.0:
  version "8.1.0"
  resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-8.1.0.tgz#f1376a33b6629a5d063782944da732631e966950"
  dependencies:
    camelcase "^4.1.0"

yargs@^10.0.3:
  version "10.1.2"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-10.1.2.tgz#454d074c2b16a51a43e2fb7807e4f9de69ccb5c5"
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.1.1"
    find-up "^2.1.0"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^8.1.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yauzl@2.4.1:
  version "2.4.1"
  resolved "https://registry.yarnpkg.com/yauzl/-/yauzl-2.4.1.tgz#9528f442dab1b2284e58b4379bb194e22e0c4005"
  dependencies:
    fd-slicer "~1.0.1"

yeast@0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/yeast/-/yeast-0.1.2.tgz#008e06d8094320c372dbc2f8ed76a0ca6c8ac419"

zip-stream@~0.5.0:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/zip-stream/-/zip-stream-0.5.2.tgz#32dcbc506d0dab4d21372625bd7ebaac3c2fff56"
  dependencies:
    compress-commons "~0.2.0"
    lodash "~3.2.0"
    readable-stream "~1.0.26"
