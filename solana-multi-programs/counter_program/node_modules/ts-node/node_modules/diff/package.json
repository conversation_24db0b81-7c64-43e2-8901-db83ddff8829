{"name": "diff", "version": "3.5.0", "description": "A javascript text diff implementation.", "keywords": ["diff", "javascript"], "maintainers": ["<PERSON> <<EMAIL>> (http://incaseofstairs.com)"], "bugs": {"email": "<EMAIL>", "url": "http://github.com/kpdecker/jsdiff/issues"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/kpdecker/jsdiff.git"}, "engines": {"node": ">=0.3.1"}, "main": "./lib", "browser": "./dist/diff.js", "scripts": {"test": "grunt"}, "dependencies": {}, "devDependencies": {"async": "^1.4.2", "babel-core": "^6.0.0", "babel-loader": "^6.0.0", "babel-preset-es2015-mod": "^6.3.13", "babel-preset-es3": "^1.0.1", "chai": "^3.3.0", "colors": "^1.1.2", "eslint": "^1.6.0", "grunt": "^0.4.5", "grunt-babel": "^6.0.0", "grunt-clean": "^0.4.0", "grunt-cli": "^0.1.13", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^1.0.0", "grunt-contrib-watch": "^1.0.0", "grunt-eslint": "^17.3.1", "grunt-karma": "^0.12.1", "grunt-mocha-istanbul": "^3.0.1", "grunt-mocha-test": "^0.12.7", "grunt-webpack": "^1.0.11", "istanbul": "github:kpdecker/istanbul", "karma": "^0.13.11", "karma-mocha": "^0.2.0", "karma-mocha-reporter": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^1.7.0", "mocha": "^2.3.3", "phantomjs-prebuilt": "^2.1.5", "semver": "^5.0.3", "webpack": "^1.12.2", "webpack-dev-server": "^1.12.0"}, "optionalDependencies": {}}