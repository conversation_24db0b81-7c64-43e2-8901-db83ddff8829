[package]
name = "counter_program"
version = "0.1.0"
edition = "2021"

[toolchain]
package_manager = "yarn"

[features]
resolution = true
skip-lint = false
seeds = true

[programs.devnet]
counter_program = "6WURob28V5zvmskZpo1uPiLaKmQgbi9SaQrCbpMXx7nk"

[lib]
name = "counter_program"
path = "programs/counter_program/src/lib.rs"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
