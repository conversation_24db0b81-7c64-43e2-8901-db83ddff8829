[package]
name = "counter_program"
version = "0.1.0"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "counter_program"

[features]
default = []
cpi = ["no-entrypoint"]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
idl-build = ["anchor-lang/idl-build"]


[dependencies]
anchor-lang = "0.31.1"
control_program = { path = "../../control_program/programs/control_program", features = ["no-entrypoint"] }

