use anchor_lang::prelude::*;

declare_id!("6WURob28V5zvmskZpo1uPiLaKmQgbi9SaQrCbpMXx7nk");

#[program]
pub mod counter_program {
    use super::*;

    pub fn initialize(ctx: Context<InitializeCounter>) -> Result<()> {
        let counter = &mut ctx.accounts.counter;
        counter.authority = ctx.accounts.authority.key();
        counter.count = 0;
        Ok(())
    }
    // 白名单才能调用
    pub fn increment(ctx:Context<Increment>) -> Result<()>{
 // CPI 调用 control_program 的 check_permission
        let cpi_program = ctx.accounts.control_program.to_account_info();
        let cpi_accounts = CheckPermission {
            access_control: ctx.accounts.access_control.to_account_info(),
        };
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);
        control_program::cpi::check_permission(cpi_ctx, ctx.accounts.authority.key())?;

        // 白名单通过后执行加1
        let counter = &mut ctx.accounts.counter;
        counter.count += 1;
        msg!("✅ 当前计数为: {}", counter.count);
        Ok(())
    }
}

#[derive(Accounts)]
pub struct InitializeCounter<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,
    #[account(
        init,
        payer = authority,
        space = 8 + Counter::MAX_SIZE,
        seeds = [b"counter", authority.key().as_ref()],
        bump
    )]
    pub counter: Account<'info, Counter>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Increment<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,

    #[account(mut, seeds = [b"counter", authority.key().as_ref()], bump)]
    pub counter: Account<'info, Counter>,

    /// CHECK: 被 control_program 验证
    #[account(seeds = [b"access"], bump)]
    pub access_control: Account<'info, AccessControl>,

    pub control_program: Program<'info, ControlProgram>,
}

#[account]
pub struct Counter {
    pub authority: Pubkey,
    pub count: u64,
}
    
impl Counter {
    pub const MAX_SIZE: usize = 32 + 8; // Pubkey + u64
}

