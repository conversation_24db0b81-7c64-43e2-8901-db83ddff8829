[package]
name = "control_program"
version = "0.1.0"
edition = "2021"

[toolchain]
package_manager = "yarn"

[features]
resolution = true
skip-lint = false
seeds = true

[programs.devnet]
control_program = "HQA1yAaS7FEYddpYgtxmwBASYAMERkGJjEdvA4r5eZS3"

[lib]
name = "control_program"
path = "programs/control_program/src/lib.rs"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
