{"address": "HQA1yAaS7FEYddpYgtxmwBASYAMERkGJjEdvA4r5eZS3", "metadata": {"name": "control_program", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "add_to_whitelist", "discriminator": [157, 211, 52, 54, 144, 81, 5, 55], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "access_control", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 99, 99, 101, 115, 115]}]}}], "args": [{"name": "user", "type": "pubkey"}]}, {"name": "check_permission", "discriminator": [154, 199, 232, 242, 96, 72, 197, 236], "accounts": [{"name": "access_control", "pda": {"seeds": [{"kind": "const", "value": [97, 99, 99, 101, 115, 115]}]}}], "args": [{"name": "user", "type": "pubkey"}]}, {"name": "initialize", "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "access_control", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 99, 99, 101, 115, 115]}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "remove_from_whitelist", "discriminator": [7, 144, 216, 239, 243, 236, 193, 235], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "access_control", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 99, 99, 101, 115, 115]}]}}], "args": [{"name": "user", "type": "pubkey"}]}], "accounts": [{"name": "AccessControl", "discriminator": [147, 81, 178, 92, 223, 66, 181, 132]}], "errors": [{"code": 6000, "name": "Unauthorized", "msg": "❌ 你不是权限所有者"}, {"code": 6001, "name": "NotInW<PERSON>elist", "msg": "❌ 该地址不在白名单中"}], "types": [{"name": "AccessControl", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "pubkey"}, {"name": "whitelist", "type": {"vec": "pubkey"}}]}}]}