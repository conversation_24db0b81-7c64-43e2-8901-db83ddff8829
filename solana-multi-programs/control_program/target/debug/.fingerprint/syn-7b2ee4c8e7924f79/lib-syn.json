{"rustc": 1951479996593678167, "features": "[\"clone-impls\", \"default\", \"derive\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 6571069320073536330, "deps": [[1988483478007900009, "unicode_ident", false, 597427228682806676], [3060637413840920116, "proc_macro2", false, 10855668114215268313], [17990358020177143287, "quote", false, 6383193348702665249]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-7b2ee4c8e7924f79/dep-lib-syn", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}