{"rustc": 1951479996593678167, "features": "[\"sha2\", \"static-context\", \"std\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 15657897354478470176, "path": 32520573025227928, "deps": [[4731167174326621189, "rand", false, 16417715877180520622], [6374421995994392543, "digest", false, 1223444884621842784], [9529943735784919782, "arrayref", false, 3136252890515521509], [9689903380558560274, "serde", false, 8550782278877608644], [10697153736615144157, "build_script_build", false, 10510285902971092328], [11472355562936271783, "sha2", false, 4237288243334581406], [13443824959912985638, "libsecp256k1_core", false, 13901492253511048390], [17072468807347166763, "base64", false, 18283607866423646835]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsecp256k1-22899380d90fd907/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}