{"rustc": 1951479996593678167, "features": "[\"default\", \"schema\"]", "declared_features": "[\"default\", \"force_exhaustive_checks\", \"schema\"]", "target": 18019366223131144178, "profile": 2225463790103693989, "path": 2629390199555782286, "deps": [[3060637413840920116, "proc_macro2", false, 10855668114215268313], [3722963349756955755, "once_cell", false, 308248993823476830], [4974441333307933176, "syn", false, 14739573101937196519], [15203748914246919255, "proc_macro_crate", false, 5625840439788841253], [17990358020177143287, "quote", false, 6383193348702665249]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-derive-e5660c885eba3e66/dep-lib-borsh_derive", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}