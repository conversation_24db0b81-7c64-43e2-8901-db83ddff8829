{"rustc": 1951479996593678167, "features": "[\"alloc\", \"default\", \"digest\", \"precomputed-tables\", \"rand_core\", \"zeroize\"]", "declared_features": "[\"alloc\", \"default\", \"digest\", \"ff\", \"group\", \"group-bits\", \"legacy_compatibility\", \"precomputed-tables\", \"rand_core\", \"serde\", \"zeroize\"]", "target": 115635582535548150, "profile": 15657897354478470176, "path": 10168638168149207191, "deps": [[1513171335889705703, "curve25519_dalek_derive", false, 8113130535528174216], [2828590642173593838, "cfg_if", false, 8653460116911003822], [6528079939221783635, "zeroize", false, 3354217665678736982], [13595581133353633439, "build_script_build", false, 15307518384354609728], [17003143334332120809, "subtle", false, 10386412372254432952], [17475753849556516473, "digest", false, 11284976383701026483], [17620084158052398167, "cpufeatures", false, 12482558356616164440], [18130209639506977569, "rand_core", false, 11450508750134963401]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-ff5eec849fa733dd/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}