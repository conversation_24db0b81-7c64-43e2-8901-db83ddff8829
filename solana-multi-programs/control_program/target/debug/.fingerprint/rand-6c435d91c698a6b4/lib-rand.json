{"rustc": 1951479996593678167, "features": "[\"alloc\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 1286431468266717412, "deps": [[1333041802001714747, "rand_chacha", false, 14101855163165204850], [1740877332521282793, "rand_core", false, 11905672962776885772], [4684437522915235464, "libc", false, 8425411174319948391], [5170503507811329045, "getrandom_package", false, 17321026141434046146]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-6c435d91c698a6b4/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}