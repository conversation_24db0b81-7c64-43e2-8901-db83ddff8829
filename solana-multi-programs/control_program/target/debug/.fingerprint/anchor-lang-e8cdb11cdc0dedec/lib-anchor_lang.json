{"rustc": 1951479996593678167, "features": "[\"anchor-lang-idl\", \"idl-build\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\", \"interface-instructions\", \"lazy-account\"]", "target": 14695202496702424983, "profile": 15657897354478470176, "path": 11299242850034576683, "deps": [[*****************, "bincode", false, 14331009917465344688], [1205890147656972920, "anchor_derive_accounts", false, 7292172626677346191], [2611905835808443941, "borsh", false, 5415956198664103277], [5532297295134723458, "anchor_attribute_program", false, 9145366249060591281], [6080285880102702883, "anchor_derive_space", false, 16666401749154253096], [6511429716036861196, "bytemuck", false, 14045554462713881325], [8008191657135824715, "thiserror", false, 12017178626872147917], [10220848352499156513, "anchor_lang_idl", false, 6726830063483385343], [10784666044722074209, "anchor_attribute_event", false, 2281974371728959656], [11737296378741312020, "anchor_attribute_account", false, 17784570588255488840], [13139933692030873282, "anchor_attribute_error", false, 6159752638768225114], [13977390777787220484, "anchor_derive_serde", false, 14095323505098991046], [14555048766774983064, "anchor_attribute_constant", false, 13142355822390974538], [16016078550530309219, "solana_program", false, 4040864876757217943], [16070395273854428984, "anchor_attribute_access_control", false, 6994715752692994443], [18066890886671768183, "base64", false, 3705883348303736075]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-e8cdb11cdc0dedec/dep-lib-anchor_lang", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}