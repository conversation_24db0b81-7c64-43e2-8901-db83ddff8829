{"rustc": 1951479996593678167, "features": "[]", "declared_features": "[\"bincode\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 15693990445969651597, "profile": 15657897354478470176, "path": 9116385785360787032, "deps": [[10570260326288551891, "solana_instruction", false, 15252086427844154269], [11091540729177102731, "solana_pubkey", false, 4933336809748014935], [14591356476411885690, "solana_sdk_ids", false, 5861885002904543542], [14666756292968957341, "solana_account_info", false, 7790895788649702327], [16041962814414187897, "solana_clock", false, 6133585423558639013]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-account-e1583371f0b8c023/dep-lib-solana_account", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}