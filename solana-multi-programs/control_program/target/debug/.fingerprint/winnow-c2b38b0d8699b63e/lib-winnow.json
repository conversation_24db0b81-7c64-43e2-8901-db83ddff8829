{"rustc": 1951479996593678167, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 12998675864389268938, "path": 17467348828741033925, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-c2b38b0d8699b63e/dep-lib-winnow", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}