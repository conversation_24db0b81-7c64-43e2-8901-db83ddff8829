{"rustc": 1951479996593678167, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2225463790103693989, "path": 2798460439628548019, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bs58-dc4d6ab4497479d8/dep-lib-bs58", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}