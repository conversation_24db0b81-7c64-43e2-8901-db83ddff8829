{"rustc": 1951479996593678167, "features": "[\"bincode\", \"blake3\", \"serde\"]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 6652793396284126538, "path": 15390064986635197532, "deps": [[65234016722529558, "bincode", false, 14331009917465344688], [4113188218898653100, "solana_short_vec", false, 1619212586725651011], [8611296141060937248, "solana_hash", false, 1903228184583643345], [9241925498456048256, "blake3", false, 3329356412222323694], [9556858120010252096, "solana_transaction_error", false, 3717683228888701527], [9689903380558560274, "serde", false, 8550782278877608644], [10570260326288551891, "solana_instruction", false, 15252086427844154269], [11091540729177102731, "solana_pubkey", false, 4933336809748014935], [11702702251883620295, "solana_bincode", false, 16875017791565400460], [14591356476411885690, "solana_sdk_ids", false, 5861885002904543542], [15341883195918613377, "solana_system_interface", false, 9813353251464911820], [15429715045911386410, "solana_sanitize", false, 16335338810058808838], [16257276029081467297, "serde_derive", false, 16958698665708194622], [17917672826516349275, "lazy_static", false, 666656900714969476]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-047582b353cd7765/dep-lib-solana_message", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}