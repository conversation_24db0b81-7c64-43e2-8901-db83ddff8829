{"rustc": 1951479996593678167, "features": "[\"bincode\", \"bytemuck\", \"serde\"]", "declared_features": "[\"bincode\", \"bytemuck\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 16616187019842477339, "profile": 6652793396284126538, "path": 2705422110477362576, "deps": [[65234016722529558, "bincode", false, 14331009917465344688], [6511429716036861196, "bytemuck", false, 14045554462713881325], [9689903380558560274, "serde", false, 8550782278877608644], [10570260326288551891, "solana_instruction", false, 15252086427844154269], [11091540729177102731, "solana_pubkey", false, 4933336809748014935], [14591356476411885690, "solana_sdk_ids", false, 5861885002904543542], [16041962814414187897, "solana_clock", false, 6133585423558639013], [16257276029081467297, "serde_derive", false, 16958698665708194622], [16847021361644352524, "solana_slot_hashes", false, 17249251140065000121]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-address-lookup-table-interface-c2750792c5d1423c/dep-lib-solana_address_lookup_table_interface", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}