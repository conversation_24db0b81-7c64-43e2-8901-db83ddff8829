{"rustc": 1951479996593678167, "features": "[\"convert\", \"heck\", \"sha2\"]", "declared_features": "[\"build\", \"convert\", \"heck\", \"regex\", \"sha2\"]", "target": 13617976458226247918, "profile": 2225463790103693989, "path": 6180100169685945356, "deps": [[9689903380558560274, "serde", false, 8550782278877608644], [9857275760291862238, "sha2", false, 18386904884155288083], [13625485746686963219, "anyhow", false, 15543424154185431119], [15367738274754116744, "serde_json", false, 5785138482272581930], [16131248048418321657, "heck", false, 15844772731309281330], [17037804673887881428, "anchor_lang_idl_spec", false, 677297481308379910]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-idl-49489e99d6cc4727/dep-lib-anchor_lang_idl", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}