{"rustc": 1951479996593678167, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 7276897073638970379, "deps": [[2828590642173593838, "cfg_if", false, 8653460116911003822], [4684437522915235464, "libc", false, 8425411174319948391]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-0c4163cd8c05317a/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}