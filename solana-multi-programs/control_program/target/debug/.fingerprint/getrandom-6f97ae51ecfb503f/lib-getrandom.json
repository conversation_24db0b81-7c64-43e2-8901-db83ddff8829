{"rustc": 1951479996593678167, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 15657897354478470176, "path": 9665518565645278117, "deps": [[2828590642173593838, "cfg_if", false, 8653460116911003822], [4684437522915235464, "libc", false, 8425411174319948391], [5170503507811329045, "build_script_build", false, 5036160568676130308]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-6f97ae51ecfb503f/dep-lib-getrandom", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}