{"rustc": 1951479996593678167, "features": "[\"serde\"]", "declared_features": "[\"dev-context-only-utils\", \"serde\"]", "target": 10340454008857965518, "profile": 15657897354478470176, "path": 5213935119161418750, "deps": [[8611296141060937248, "solana_hash", false, 1903228184583643345], [9689903380558560274, "serde", false, 8550782278877608644], [11091540729177102731, "solana_pubkey", false, 4933336809748014935], [11104455582174147483, "solana_sha256_hasher", false, 17065250504645907801], [16257276029081467297, "serde_derive", false, 16958698665708194622], [17802518109446470116, "solana_fee_calculator", false, 14327504580763262360]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-nonce-733751280bdc8d26/dep-lib-solana_nonce", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}