{"rustc": 1951479996593678167, "features": "[]", "declared_features": "[]", "target": 13909130799607605443, "profile": 15657897354478470176, "path": 7122045324367532936, "deps": [[730322694175296062, "solana_address_lookup_table_interface", false, 251839129790150331], [4043952427700520164, "solana_nonce", false, 7905837645043240742], [8611296141060937248, "solana_hash", false, 1903228184583643345], [9689903380558560274, "serde", false, 8550782278877608644], [10570260326288551891, "solana_instruction", false, 15252086427844154269], [10806645703491011684, "thiserror", false, 4272252939043482664], [11087274787214030812, "solana_keccak_hasher", false, 908991639468272200], [11091540729177102731, "solana_pubkey", false, 4933336809748014935], [14591356476411885690, "solana_sdk_ids", false, 5861885002904543542], [15341883195918613377, "solana_system_interface", false, 9813353251464911820], [16041962814414187897, "solana_clock", false, 6133585423558639013], [16257276029081467297, "serde_derive", false, 16958698665708194622], [16605237980896264354, "solana_message", false, 646302446676885947]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-example-mocks-beb65a181bc81117/dep-lib-solana_example_mocks", "checksum": false}}], "rustflags": ["--cfg", "procmacro2_semver_exempt", "-A", "warnings"], "config": 2069994364910194474, "compile_kind": 0}