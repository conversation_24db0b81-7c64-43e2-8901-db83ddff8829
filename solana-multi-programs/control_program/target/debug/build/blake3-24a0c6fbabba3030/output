cargo:rustc-check-cfg=cfg(blake3_sse2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx512_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_neon, values(none()))
cargo:rustc-check-cfg=cfg(blake3_wasm32_simd, values(none()))
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
OUT_DIR = Some(/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
CC_x86_64-unknown-linux-gnu = None
CC_x86_64_unknown_linux_gnu = None
HOST_CC = None
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
OUT_DIR = Some(/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-gnu)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
HOST = Some(x86_64-unknown-linux-gnu)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_unknown_linux_gnu = None
CFLAGS_x86_64-unknown-linux-gnu = None
OUT_DIR = Some(/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-gnu)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
HOST = Some(x86_64-unknown-linux-gnu)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_unknown_linux_gnu = None
CFLAGS_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rustc-cfg=blake3_sse2_ffi
cargo:rustc-cfg=blake3_sse41_ffi
cargo:rustc-cfg=blake3_avx2_ffi
OUT_DIR = Some(/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
CC_x86_64-unknown-linux-gnu = None
CC_x86_64_unknown_linux_gnu = None
HOST_CC = None
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_unknown_linux_gnu = None
CFLAGS_x86_64-unknown-linux-gnu = None
CARGO_ENCODED_RUSTFLAGS = Some(--cfgprocmacro2_semver_exempt-Awarnings)
AR_x86_64-unknown-linux-gnu = None
AR_x86_64_unknown_linux_gnu = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_unknown_linux_gnu = None
ARFLAGS_x86_64-unknown-linux-gnu = None
cargo:rustc-link-lib=static=blake3_sse2_sse41_avx2_assembly
cargo:rustc-link-search=native=/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rustc-cfg=blake3_avx512_ffi
OUT_DIR = Some(/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
CC_x86_64-unknown-linux-gnu = None
CC_x86_64_unknown_linux_gnu = None
HOST_CC = None
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_unknown_linux_gnu = None
CFLAGS_x86_64-unknown-linux-gnu = None
CARGO_ENCODED_RUSTFLAGS = Some(--cfgprocmacro2_semver_exempt-Awarnings)
AR_x86_64-unknown-linux-gnu = None
AR_x86_64_unknown_linux_gnu = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_unknown_linux_gnu = None
ARFLAGS_x86_64-unknown-linux-gnu = None
cargo:rustc-link-lib=static=blake3_avx512_assembly
cargo:rustc-link-search=native=/home/<USER>/solana/solana-multi-programs/control_program/target/debug/build/blake3-24a0c6fbabba3030/out
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CC
cargo:rerun-if-env-changed=CFLAGS
cargo:rerun-if-changed=c/cmake
cargo:rerun-if-changed=c/blake3_sse41_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/CMakePresets.json
cargo:rerun-if-changed=c/Makefile.testing
cargo:rerun-if-changed=c/CMakeLists.txt
cargo:rerun-if-changed=c/dependencies
cargo:rerun-if-changed=c/.gitignore
cargo:rerun-if-changed=c/blake3_avx2_x86-64_unix.S
cargo:rerun-if-changed=c/blake3_avx512_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/blake3_dispatch.c
cargo:rerun-if-changed=c/test.py
cargo:rerun-if-changed=c/blake3_portable.c
cargo:rerun-if-changed=c/blake3_sse2.c
cargo:rerun-if-changed=c/blake3_sse41_x86-64_unix.S
cargo:rerun-if-changed=c/blake3-config.cmake.in
cargo:rerun-if-changed=c/blake3.h
cargo:rerun-if-changed=c/blake3_avx2.c
cargo:rerun-if-changed=c/blake3_sse2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/libblake3.pc.in
cargo:rerun-if-changed=c/blake3_impl.h
cargo:rerun-if-changed=c/blake3_tbb.cpp
cargo:rerun-if-changed=c/blake3.c
cargo:rerun-if-changed=c/example_tbb.c
cargo:rerun-if-changed=c/blake3_avx2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/blake3_neon.c
cargo:rerun-if-changed=c/README.md
cargo:rerun-if-changed=c/blake3_sse41_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/example.c
cargo:rerun-if-changed=c/blake3_avx2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/main.c
cargo:rerun-if-changed=c/blake3_sse2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/blake3_sse41.c
cargo:rerun-if-changed=c/blake3_avx512_x86-64_unix.S
cargo:rerun-if-changed=c/blake3_sse2_x86-64_unix.S
cargo:rerun-if-changed=c/blake3_avx512_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/blake3_avx512.c
